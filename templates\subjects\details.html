{% extends "dashboard.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<style>
    .subject-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .info-card {
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-radius: 10px;
        transition: all 0.3s ease;
    }
    
    .info-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .rating-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
    }
    
    .course-item {
        border-left: 4px solid #667eea;
        background: white;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .rating-stars {
        color: #ffc107;
        font-size: 1.2rem;
    }
    
    .rating-form {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }
    
    .file-preview {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        margin: 1rem 0;
    }
    
    .tag-badge {
        background: #e9ecef;
        color: #495057;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        margin: 0.2rem;
        display: inline-block;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-item {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    
    <!-- Subject Header -->
    <div class="subject-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 mb-2">
                    <i class="fas fa-book me-2"></i>
                    {{ subject.name }}
                </h1>
                <div class="mb-3">
                    <span class="badge bg-light text-dark me-2">{{ subject.category }}</span>
                    <span class="badge bg-secondary me-2">{{ subject.level }}</span>
                    <span class="badge bg-info">{{ subject.duration_hours }} ساعة</span>
                </div>
                <div class="rating-stars mb-2">
                    {% for i in range(1, 6) %}
                        {% if i <= avg_rating %}
                            <i class="fas fa-star"></i>
                        {% else %}
                            <i class="far fa-star"></i>
                        {% endif %}
                    {% endfor %}
                    <span class="text-white ms-2">({{ avg_rating }}/5)</span>
                </div>
                {% if subject.description %}
                <p class="mb-0 opacity-75">{{ subject.description }}</p>
                {% endif %}
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group">
                    {% if current_user.role == 'admin' or current_user.id == subject.created_by %}
                    <a href="{{ url_for('edit_subject', subject_id=subject.id) }}" class="btn btn-light">
                        <i class="fas fa-edit me-1"></i> تعديل
                    </a>
                    {% endif %}
                    {% if subject.file_path %}
                    <a href="{{ url_for('download_subject_file', subject_id=subject.id) }}" class="btn btn-success">
                        <i class="fas fa-download me-1"></i> تحميل الملف
                    </a>
                    {% endif %}
                    <a href="{{ url_for('subjects_index') }}" class="btn btn-outline-light">
                        <i class="fas fa-arrow-right me-1"></i> العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-item">
            <div class="stat-value">{{ subject.usage_stats.total_courses if subject.usage_stats else 0 }}</div>
            <div class="stat-label">الدورات المرتبطة</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ subject.usage_stats.total_participants if subject.usage_stats else 0 }}</div>
            <div class="stat-label">إجمالي المشاركين</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ subject.usage_stats.total_downloads if subject.usage_stats else 0 }}</div>
            <div class="stat-label">مرات التحميل</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ subject.usage_stats.total_views if subject.usage_stats else 0 }}</div>
            <div class="stat-label">مرات المشاهدة</div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            
            <!-- File Preview -->
            {% if subject.file_path %}
            <div class="info-card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file me-2"></i>الملف المرفق
                    </h5>
                </div>
                <div class="card-body">
                    <div class="file-preview">
                        <i class="fas fa-file-{{ 'pdf' if subject.file_type == 'pdf' else 'alt' }} fa-3x text-muted mb-3"></i>
                        <h6>{{ subject.name }}.{{ subject.file_type }}</h6>
                        <p class="text-muted">حجم الملف: {{ "%.2f"|format((subject.file_size / 1024 / 1024) if subject.file_size else 0) }} ميجابايت</p>
                        <a href="{{ url_for('download_subject_file', subject_id=subject.id) }}" class="btn btn-primary">
                            <i class="fas fa-download me-1"></i> تحميل الملف
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Preview Content -->
            {% if subject.preview_content %}
            <div class="info-card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>معاينة المحتوى
                    </h5>
                </div>
                <div class="card-body">
                    <div class="preview-content">
                        {{ subject.preview_content|nl2br }}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Related Courses -->
            <div class="info-card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>الدورات المرتبطة ({{ course_subjects|length }})
                    </h5>
                </div>
                <div class="card-body">
                    {% if course_subjects %}
                        {% for course_subject, course in course_subjects %}
                        <div class="course-item">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h6 class="mb-1">
                                        <a href="{{ url_for('course_details', course_id=course.id) }}" class="text-decoration-none">
                                            {{ course.title }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">رقم الدورة: {{ course.course_number }}</small>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">اليوم {{ course_subject.day_number }}</small><br>
                                    <small class="text-primary">{{ course_subject.start_time }} - {{ course_subject.end_time }}</small>
                                </div>
                                <div class="col-md-3 text-end">
                                    <span class="badge bg-primary">{{ course.category }}</span>
                                </div>
                            </div>
                            {% if course_subject.notes %}
                            <div class="mt-2">
                                <small class="text-muted">{{ course_subject.notes }}</small>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد دورات مرتبطة بهذه المادة</h6>
                            <p class="text-muted">يمكن إضافة هذه المادة للدورات من خلال إدارة الدورات</p>
                        </div>
                    {% endif %}
                </div>
            </div>

        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            
            <!-- Subject Info -->
            <div class="info-card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات المادة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-6"><strong>الفئة:</strong></div>
                        <div class="col-6">{{ subject.category }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6"><strong>المستوى:</strong></div>
                        <div class="col-6">{{ subject.level }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6"><strong>المدة:</strong></div>
                        <div class="col-6">{{ subject.duration_hours }} ساعة</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6"><strong>المنشئ:</strong></div>
                        <div class="col-6">{{ subject.creator.username if subject.creator else 'غير محدد' }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6"><strong>تاريخ الإنشاء:</strong></div>
                        <div class="col-6">{{ subject.created_at.strftime('%Y-%m-%d') }}</div>
                    </div>
                    {% if subject.updated_at != subject.created_at %}
                    <div class="row mb-2">
                        <div class="col-6"><strong>آخر تحديث:</strong></div>
                        <div class="col-6">{{ subject.updated_at.strftime('%Y-%m-%d') }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Tags -->
            {% if subject.tags %}
            <div class="info-card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-tags me-2"></i>الكلمات المفتاحية
                    </h6>
                </div>
                <div class="card-body">
                    {% for tag in subject.tags.split(',') %}
                        {% if tag.strip() %}
                        <span class="tag-badge">{{ tag.strip() }}</span>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Rating Form -->
            <div class="rating-form mb-4">
                <h6 class="mb-3">
                    <i class="fas fa-star me-2 text-warning"></i>تقييم المادة
                </h6>
                
                {% if user_rating %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لقد قمت بتقييم هذه المادة مسبقاً. يمكنك تحديث تقييمك أدناه.
                </div>
                {% endif %}
                
                <form method="POST" action="{{ url_for('rate_subject', subject_id=subject.id) }}">
                    {{ rating_form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ rating_form.rating.label(class="form-label") }}
                        {{ rating_form.rating(class="form-select") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ rating_form.comment.label(class="form-label") }}
                        {{ rating_form.comment(class="form-control", rows="3", placeholder="اكتب تعليقك حول المادة...") }}
                    </div>
                    
                    <div class="text-center">
                        {{ rating_form.submit(class="btn btn-warning") }}
                    </div>
                </form>
            </div>

            <!-- Recent Ratings -->
            {% if ratings %}
            <div class="info-card">
                <div class="card-header bg-dark text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-comments me-2"></i>آخر التقييمات
                    </h6>
                </div>
                <div class="card-body">
                    {% for rating in ratings %}
                    <div class="border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>{{ rating.user.username }}</strong>
                                <div class="rating-stars small">
                                    {% for i in range(1, 6) %}
                                        {% if i <= rating.rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            <small class="text-muted">{{ rating.created_at.strftime('%Y-%m-%d') }}</small>
                        </div>
                        {% if rating.comment %}
                        <p class="small text-muted mt-1 mb-0">{{ rating.comment }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

        </div>
    </div>

</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تأثيرات بصرية للبطاقات
    $('.info-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
    
    // تحديث عداد المشاهدات (تم بالفعل في الخادم)
    console.log('تم تحديث عداد المشاهدات للمادة: {{ subject.name }}');
});
</script>
{% endblock %}
