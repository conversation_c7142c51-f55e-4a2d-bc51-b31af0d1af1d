<!-- نقطة التراجع الأولى: تم إنشاؤها بعد إكمال الرسوم البيانية وتصدير Excel لصفحة تحليل كشف الدورة -->
<!-- نقطة التراجع الثانية: تم إنشاؤها بعد توحيد التصميم والخط وتنظيم الأزرار في صفحة التقييمات -->
{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap" rel="stylesheet">
<style>
    body, h1, h2, h3, h4, h5, h6, p, span, div, small, strong, .card-body, .stat-label, .stat-number, .btn, .badge, .alert {
        font-family: 'Cairo', sans-serif !important;
    }
    .stats-card {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 25px 20px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 900;
        margin: 15px 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        color: #ffffff;
        line-height: 1;
    }

    .stat-label {
        font-size: 1.1rem;
        font-weight: 600;
        opacity: 0.95;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        color: #f8f9fa;
        line-height: 1.3;
    }

    .result-section {
        background: white;
        border-radius: 15px;
        padding: 0;
        margin-bottom: 25px;
        box-shadow: 0 10px 25px rgba(0, 123, 255, 0.1);
        border: 1px solid rgba(0, 123, 255, 0.1);
        overflow: hidden;
    }

    .name-item {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid rgba(0, 123, 255, 0.1);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.05);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .name-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .name-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 123, 255, 0.1);
    }

    .corrected-name::before {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    }

    .similar-name::before {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    .new-name::before {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    }

    .btn-export {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 25px;
        color: white;
        transition: all 0.3s ease;
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
        color: white;
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    }

    .btn-export-secondary {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border: none;
        padding: 10px 25px;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 20px;
        color: white;
        transition: all 0.3s ease;
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
    }

    .btn-export-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        color: white;
        background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    }

    .btn-export-secondary:focus {
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        color: white;
    }

    .section-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 20px;
        margin-bottom: 0;
        position: relative;
        overflow: hidden;
    }

    .section-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .badge-custom {
        font-size: 0.8rem;
        padding: 5px 10px;
        border-radius: 15px;
    }

    .progress-custom {
        height: 8px;
        border-radius: 10px;
        background-color: #e9ecef;
    }

    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .evaluation-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 25px;
        box-shadow: 0 10px 25px rgba(0, 123, 255, 0.2);
        position: relative;
        overflow: hidden;
    }

    .evaluation-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .evaluation-stats {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    /* تحسين البطاقات */
    .card {
        border-radius: 15px;
        box-shadow: 0 8px 20px rgba(0, 123, 255, 0.1);
        border: 1px solid rgba(0, 123, 255, 0.1);
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0, 123, 255, 0.15);
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: 1px solid rgba(0, 123, 255, 0.1);
    }

    .card-body {
        border-radius: 0 0 15px 15px;
    }

    /* تحسين التنبيهات */
    .alert {
        border-radius: 12px;
        border: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .alert::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: currentColor;
    }

    /* تحسين تخطيط الأزرار */
    .btn {
        min-width: 200px;
        text-align: center;
        white-space: nowrap;
        font-weight: 600;
    }

    .btn i {
        margin-left: 8px;
    }

    .gap-2 {
        gap: 0.5rem !important;
    }

    /* تحسين الاستجابة */
    @media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
        }

        .text-end {
            text-align: center !important;
        }

        .btn {
            min-width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h1 class="text-primary mb-2">
                        <i class="fas fa-star"></i> نتائج تحليل كشف التقييمات
                    </h1>
                    <p class="text-muted mb-3">
                        <i class="fas fa-file-excel"></i> الملف: {{ excel_filename }}
                    </p>
                    {% if selected_course %}
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-graduation-cap"></i>
                        <strong>الدورة المختارة:</strong> {{ selected_course.course_number }} - {{ selected_course.title }}
                    </div>
                    {% endif %}
                </div>
                <div class="text-end">
                    <div class="d-flex flex-column gap-2">
                        <a href="{{ url_for('person_data.export_analysis_results') }}"
                           class="btn btn-export"
                           title="تصدير تقرير شامل يحتوي على: لديهم تقييم، ليس لديهم تقييم، تقييمات جديدة، تقييمات مرفوضة، وجميع التفاصيل">
                            <i class="fas fa-download"></i> تصدير التقرير الكامل
                        </a>
                        <a href="{{ url_for('person_data.export_new_names_only') }}"
                           class="btn btn-export-secondary">
                            <i class="fas fa-file-excel"></i> تصدير الجديدة فقط
                        </a>

                        <a href="{{ url_for('person_data.name_analysis') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> تحليل جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards - الفئات الأربعة الجديدة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center bg-gradient-primary">
                <div class="stat-number text-white">{{ results.statistics.total_processed }}</div>
                <div class="stat-label text-white">إجمالي التقييمات المعالجة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center bg-gradient-success">
                <div class="stat-number text-white">{{ results.statistics.category_1_count }}</div>
                <div class="stat-label text-white">الفئة 1: غير موجود في القاعدة</div>
                <small class="text-white-50">سيتم إنشاؤه + إضافته للدورة + تقييمه</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center bg-gradient-info">
                <div class="stat-number text-white">{{ results.statistics.category_2_count }}</div>
                <div class="stat-label text-white">الفئة 2: في القاعدة والدورة بدون تقييم</div>
                <small class="text-white-50">سيتم إضافة التقييم فقط</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center bg-gradient-warning">
                <div class="stat-number text-white">{{ results.statistics.category_3_count }}</div>
                <div class="stat-label text-white">الفئة 3: في القاعدة وليس في الدورة</div>
                <small class="text-white-50">سيتم إضافته للدورة + تقييمه</small>
            </div>
        </div>
    </div>

    <!-- الصف الثاني للإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center bg-gradient-secondary">
                <div class="stat-number text-white">{{ results.statistics.category_4_count }}</div>
                <div class="stat-label text-white">الفئة 4: في القاعدة والدورة ولديه تقييم</div>
                <small class="text-white-50">سيتم تحديث تقييمه</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center bg-gradient-danger">
                <div class="stat-number text-white">{{ results.statistics.blocked_duplicates_count }}</div>
                <div class="stat-label text-white">سجلات مرفوضة</div>
                <small class="text-white-50">تطابق في البيانات - مرفوض</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center bg-gradient-dark">
                <div class="stat-number text-white">{{ results.statistics.corrected_count }}</div>
                <div class="stat-label text-white">أسماء مصححة</div>
                <small class="text-white-50">تم تصحيحها تلقائياً</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center bg-gradient-light">
                <div class="stat-number">{{ "%.1f"|format(results.statistics.success_rate) }}%</div>
                <div class="stat-label">معدل النجاح</div>
                <small class="text-muted">نسبة السجلات المقبولة</small>
            </div>
        </div>
    </div>

    <!-- Advanced Duplicate Detection Cards -->
    {% if results.statistics.has_national_id_column or results.statistics.has_phone_column or results.statistics.has_military_id_column %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-search"></i> نتائج فحص التطابق المتقدم للتقييمات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="card text-center border-danger">
                                <div class="card-body">
                                    <h5 class="text-danger">{{ results.statistics.blocked_duplicates_count }}</h5>
                                    <small class="text-muted">تقييمات مرفوضة</small>
                                    <br><small class="text-danger">تطابق في البيانات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card text-center border-success">
                                <div class="card-body">
                                    <h5 class="text-success">{{ results.statistics.allowed_duplicates_count }}</h5>
                                    <small class="text-muted">أسماء مكررة مسموحة</small>
                                    <br><small class="text-success">بيانات مختلفة</small>
                                </div>
                            </div>
                        </div>
                        {% if results.statistics.has_national_id_column %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.name_national_id_matches + results.statistics.national_id_only_matches }}</h5>
                                    <small class="text-muted">تطابق رقم وطني</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if results.statistics.has_phone_column %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.name_phone_matches + results.statistics.phone_only_matches }}</h5>
                                    <small class="text-muted">تطابق رقم هاتف</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if results.statistics.has_military_id_column %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.name_military_id_matches + results.statistics.military_id_only_matches }}</h5>
                                    <small class="text-muted">تطابق رقم عسكري</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if results.course_analysis and results.course_analysis.selected_course %}
    <!-- تحليل التقييمات للدورة المختارة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-star"></i> تحليل التقييمات للدورة
                    </h5>
                </div>
                <div class="card-body">
                    <!-- معلومات الدورة -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-warning">معلومات الدورة:</h6>
                            <p class="mb-1"><strong>رقم الدورة:</strong> {{ results.course_analysis.selected_course.course_number }}</p>
                            <p class="mb-1"><strong>اسم الدورة:</strong> {{ results.course_analysis.selected_course.title }}</p>
                            <p class="mb-1"><strong>الجهة:</strong> {{ results.course_analysis.selected_course.agency or 'غير محدد' }}</p>
                            <p class="mb-0"><strong>المركز:</strong> {{ results.course_analysis.selected_course.center_name or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">إحصائيات التقييمات:</h6>
                            {% if results.course_analysis.participants_summary %}
                            <p class="mb-1"><strong>التقييمات الحالية:</strong>
                                <span class="badge bg-info">{{ results.course_analysis.participants_summary.current_participants_count }}</span>
                            </p>
                            <p class="mb-1"><strong>التقييمات الجديدة:</strong>
                                <span class="badge bg-success">{{ results.course_analysis.participants_summary.new_participants_count }}</span>
                            </p>
                            <p class="mb-1"><strong>التقييمات المكررة:</strong>
                                <span class="badge bg-warning">{{ results.course_analysis.participants_summary.duplicate_participants_count }}</span>
                            </p>
                            <p class="mb-0"><strong>إجمالي بعد الاستيراد:</strong>
                                <span class="badge bg-primary">{{ results.course_analysis.participants_summary.total_after_import }}</span>
                            </p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- تحذيرات التقييمات المكررة -->
                    {% if results.course_analysis.duplicate_participants %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> تم العثور على {{ results.course_analysis.duplicate_participants|length }} تقييم مكرر في كشف التقييمات.
                        هذه التقييمات موجودة مسبقاً في الدورة ولن يتم إضافتها مرة أخرى.
                    </div>

                    <!-- عرض التقييمات المكررة -->
                    <div class="mb-3">
                        <h6 class="text-warning">التقييمات المكررة في الدورة:</h6>
                        <div class="row">
                            {% for duplicate in results.course_analysis.duplicate_participants[:5] %}
                            <div class="col-md-6 mb-2">
                                <div class="card border-warning">
                                    <div class="card-body p-2">
                                        <h6 class="card-title text-warning mb-1">{{ duplicate.name }}</h6>
                                        <small class="text-muted">{{ duplicate.reason }}</small>
                                        <br><small class="badge bg-warning">موجود في الصف {{ duplicate.excel_record.row_index }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if results.course_analysis.duplicate_participants|length > 5 %}
                        <p class="text-muted text-center mt-2">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض أول 5 تقييمات مكررة فقط. للاطلاع على القائمة الكاملة، قم بتصدير النتائج.
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- التقييمات الجديدة للدورة -->
                    {% if results.course_analysis.new_participants %}
                    <div class="mb-3">
                        <h6 class="text-success">التقييمات الجديدة التي سيتم إضافتها للدورة:</h6>
                        <div class="row">
                            {% for new_evaluation in results.course_analysis.new_participants[:5] %}
                            <div class="col-md-6 mb-2">
                                <div class="card border-success">
                                    <div class="card-body p-2">
                                        <h6 class="card-title text-success mb-1">{{ new_evaluation.name }}</h6>
                                        <small class="badge bg-success">{{ new_evaluation.type }}</small>
                                        <br><small class="text-muted">من الصف {{ new_evaluation.excel_record.row_index }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if results.course_analysis.new_participants|length > 5 %}
                        <p class="text-muted text-center mt-2">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض أول 5 تقييمات جديدة فقط. للاطلاع على القائمة الكاملة، قم بتصدير النتائج.
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- زر تحديث التقييمات المدخلة -->
                    <div class="text-center mt-4">
                        {% if results.exact_matches %}
                        <button type="button" class="btn btn-info btn-lg me-3" data-bs-toggle="modal" data-bs-target="#updateEvaluationsModal">
                            <i class="fas fa-edit"></i> تحديث التقييمات المدخلة ({{ results.exact_matches|length }})
                        </button>
                        {% endif %}
                        {% if results.course_analysis.new_participants %}
                        <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-success btn-lg"
                                    onclick="return confirm('هل تريد إضافة {{ results.course_analysis.new_participants|length }} تقييم جديد للدورة وقاعدة البيانات؟')">
                                <i class="fas fa-plus-circle"></i> إضافة التقييمات الجديدة
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- الفئة 1: غير موجودين في قاعدة البيانات -->
    {% if results.category_1 %}
    <div class="result-section">
        <div class="section-header bg-success text-white">
            <h4 class="mb-0">
                <i class="fas fa-user-plus"></i> الفئة 1: غير موجودين في قاعدة البيانات
                <span class="badge bg-light text-success">{{ results.category_1|length }}</span>
            </h4>
            <p class="mb-0 mt-2"><small>سيتم إنشاؤهم في القاعدة + إضافتهم للدورة + إدخال تقييماتهم</small></p>
        </div>
        <div class="p-3">
            <div class="alert alert-success border-success">
                <i class="fas fa-plus-circle"></i>
                <strong>الإجراء المطلوب:</strong> إنشاء ملفات شخصية جديدة + إضافة للدورة + إدخال التقييمات
                <br><small class="text-muted">هؤلاء الأشخاص غير موجودين في النظام نهائياً</small>
            </div>

            <div class="text-center mb-3">
                <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="import_type" value="category_1">
                    <button type="submit" class="btn btn-success btn-lg shadow"
                            onclick="return confirm('هل تريد إنشاء {{ results.category_1|length }} شخص جديد وإضافتهم للقاعدة والدورة والتقييم؟\n\nسيتم:\n- إنشاء ملف شخصي جديد\n- إضافة للدورة\n- إدخال التقييم')">
                        <i class="fas fa-plus-circle"></i> إضافة للقاعدة والدورة والتقييم ({{ results.category_1|length }})
                    </button>
                </form>
            </div>

            {% for record in results.category_1[:10] %}
            <div class="card mb-2 border-success shadow-sm">
                <div class="card-body p-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="text-success mb-1">{{ record.name }}</h6>
                            {% if record.was_corrected %}
                            <small class="text-muted">الأصلي: {{ record.original_name }}</small><br>
                            {% endif %}
                            <small class="text-muted">الصف: {{ record.excel_record.row_index }}</small>

                            <div class="mt-2">
                                {% if record.excel_record.national_id %}
                                <span class="badge bg-info me-1">رقم وطني: {{ record.excel_record.national_id }}</span>
                                {% endif %}
                                {% if record.excel_record.phone %}
                                <span class="badge bg-success me-1">هاتف: {{ record.excel_record.phone }}</span>
                                {% endif %}
                                {% if record.excel_record.military_id %}
                                <span class="badge bg-warning me-1">عسكري: {{ record.excel_record.military_id }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            {% if record.excel_record.grade %}
                            <small class="text-muted">الدرجة:</small>
                            <h6 class="text-primary">{{ record.excel_record.grade }}</h6>
                            {% endif %}
                            {% if record.excel_record.percentage %}
                            <small class="text-muted">النسبة:</small>
                            <h6 class="text-success">{{ record.excel_record.percentage }}%</h6>
                            {% endif %}
                        </div>
                        <div class="col-md-3 text-end">
                            <span class="badge bg-success fs-6 p-2">
                                <i class="fas fa-plus-circle"></i> جديد كلياً
                            </span>
                            <br><small class="text-muted mt-1">قاعدة + دورة + تقييم</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            {% if results.category_1|length > 10 %}
            <div class="text-center mt-3">
                <p class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 سجلات فقط من أصل {{ results.category_1|length }}. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- الفئة 2: موجودين في القاعدة والدورة وغير مدخل لهم تقييم -->
    {% if results.category_2 %}
    <div class="result-section">
        <div class="section-header bg-info text-white">
            <h4 class="mb-0">
                <i class="fas fa-star"></i> الفئة 2: موجودين في القاعدة والدورة - بدون تقييم
                <span class="badge bg-light text-info">{{ results.category_2|length }}</span>
            </h4>
            <p class="mb-0 mt-2"><small>سيتم إضافة التقييمات لهم فقط</small></p>
        </div>
        <div class="p-3">
            <div class="alert alert-info border-info">
                <i class="fas fa-star"></i>
                <strong>الإجراء المطلوب:</strong> إضافة التقييمات فقط
                <br><small class="text-muted">هؤلاء الأشخاص موجودين في القاعدة ومسجلين في الدورة لكن ليس لديهم تقييم</small>
            </div>

            <div class="text-center mb-3">
                <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="import_type" value="category_2">
                    <button type="submit" class="btn btn-info btn-lg shadow"
                            onclick="return confirm('هل تريد إضافة التقييمات لـ {{ results.category_2|length }} شخص؟\n\nسيتم:\n- إضافة التقييم فقط\n- لن يتم تعديل البيانات الشخصية\n- لن يتم إضافة للدورة (موجود مسبقاً)')">
                        <i class="fas fa-star"></i> إضافة التقييمات فقط ({{ results.category_2|length }})
                    </button>
                </form>
            </div>

            {% for record in results.category_2[:10] %}
            <div class="card mb-2 border-info shadow-sm">
                <div class="card-body p-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="text-info mb-1">{{ record.name }}</h6>
                            {% if record.was_corrected %}
                            <small class="text-muted">الأصلي: {{ record.original_name }}</small><br>
                            {% endif %}
                            <small class="text-muted">الصف: {{ record.excel_record.row_index }}</small>
                            <br><small class="text-success"><i class="fas fa-check"></i> موجود في القاعدة والدورة</small>

                            <div class="mt-2">
                                {% if record.db_record.national_id %}
                                <span class="badge bg-secondary me-1">رقم وطني: {{ record.db_record.national_id }}</span>
                                {% endif %}
                                {% if record.db_record.phone %}
                                <span class="badge bg-secondary me-1">هاتف: {{ record.db_record.phone }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            {% if record.excel_record.grade %}
                            <small class="text-muted">الدرجة الجديدة:</small>
                            <h6 class="text-primary">{{ record.excel_record.grade }}</h6>
                            {% endif %}
                            {% if record.excel_record.percentage %}
                            <small class="text-muted">النسبة الجديدة:</small>
                            <h6 class="text-success">{{ record.excel_record.percentage }}%</h6>
                            {% endif %}
                        </div>
                        <div class="col-md-3 text-end">
                            <span class="badge bg-info fs-6 p-2">
                                <i class="fas fa-star"></i> إضافة تقييم
                            </span>
                            <br><small class="text-muted mt-1">تقييم فقط</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            {% if results.category_2|length > 10 %}
            <div class="text-center mt-3">
                <p class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 سجلات فقط من أصل {{ results.category_2|length }}. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- الفئة 3: موجودين في القاعدة وغير موجودين في الدورة -->
    {% if results.category_3 %}
    <div class="result-section">
        <div class="section-header bg-warning text-dark">
            <h4 class="mb-0">
                <i class="fas fa-user-plus"></i> الفئة 3: موجودين في القاعدة - غير موجودين في الدورة
                <span class="badge bg-dark text-warning">{{ results.category_3|length }}</span>
            </h4>
            <p class="mb-0 mt-2"><small>سيتم إضافتهم للدورة + إدخال تقييماتهم</small></p>
        </div>
        <div class="p-3">
            <div class="alert alert-warning border-warning">
                <i class="fas fa-user-plus"></i>
                <strong>الإجراء المطلوب:</strong> إضافة للدورة + إدخال التقييم
                <br><small class="text-muted">هؤلاء الأشخاص موجودين في القاعدة لكن غير مسجلين في هذه الدورة</small>
            </div>

            <div class="text-center mb-3">
                <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="import_type" value="category_3">
                    <button type="submit" class="btn btn-warning btn-lg shadow"
                            onclick="return confirm('هل تريد إضافة {{ results.category_3|length }} شخص للدورة والتقييم؟\n\nسيتم:\n- إضافة للدورة\n- إدخال التقييم\n- لن يتم تعديل البيانات الشخصية (موجود مسبقاً)')">
                        <i class="fas fa-user-plus"></i> إضافة للدورة والتقييم ({{ results.category_3|length }})
                    </button>
                </form>
            </div>

            {% for record in results.category_3[:10] %}
            <div class="card mb-2 border-warning shadow-sm">
                <div class="card-body p-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="text-warning mb-1">{{ record.name }}</h6>
                            {% if record.was_corrected %}
                            <small class="text-muted">الأصلي: {{ record.original_name }}</small><br>
                            {% endif %}
                            <small class="text-muted">الصف: {{ record.excel_record.row_index }}</small>
                            <br><small class="text-success"><i class="fas fa-check"></i> موجود في القاعدة</small>
                            <br><small class="text-danger"><i class="fas fa-times"></i> غير مسجل في الدورة</small>

                            <div class="mt-2">
                                {% if record.db_record.national_id %}
                                <span class="badge bg-secondary me-1">رقم وطني: {{ record.db_record.national_id }}</span>
                                {% endif %}
                                {% if record.db_record.phone %}
                                <span class="badge bg-secondary me-1">هاتف: {{ record.db_record.phone }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            {% if record.excel_record.grade %}
                            <small class="text-muted">الدرجة:</small>
                            <h6 class="text-primary">{{ record.excel_record.grade }}</h6>
                            {% endif %}
                            {% if record.excel_record.percentage %}
                            <small class="text-muted">النسبة:</small>
                            <h6 class="text-success">{{ record.excel_record.percentage }}%</h6>
                            {% endif %}
                        </div>
                        <div class="col-md-3 text-end">
                            <span class="badge bg-warning text-dark fs-6 p-2">
                                <i class="fas fa-user-plus"></i> دورة + تقييم
                            </span>
                            <br><small class="text-muted mt-1">إضافة للدورة</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            {% if results.category_3|length > 10 %}
            <div class="text-center mt-3">
                <p class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 سجلات فقط من أصل {{ results.category_3|length }}. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- الفئة 4: موجودين في القاعدة والدورة ولديهم تقييم -->
    {% if results.category_4 %}
    <div class="result-section">
        <div class="section-header bg-secondary text-white">
            <h4 class="mb-0">
                <i class="fas fa-edit"></i> الفئة 4: موجودين في القاعدة والدورة ولديهم تقييم
                <span class="badge bg-light text-secondary">{{ results.category_4|length }}</span>
            </h4>
            <p class="mb-0 mt-2"><small>سيتم تحديث تقييماتهم الموجودة</small></p>
        </div>
        <div class="p-3">
            <div class="alert alert-secondary border-secondary">
                <i class="fas fa-edit"></i>
                <strong>الإجراء المطلوب:</strong> تحديث التقييمات الموجودة
                <br><small class="text-muted">هؤلاء الأشخاص موجودين في القاعدة ومسجلين في الدورة ولديهم تقييم سابق</small>
            </div>

            <div class="text-center mb-3">
                <form method="post" action="{{ url_for('person_data.update_evaluations') }}" class="d-inline">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="import_type" value="category_4">
                    <button type="submit" class="btn btn-secondary btn-lg shadow"
                            onclick="return confirm('هل تريد تحديث التقييمات لـ {{ results.category_4|length }} شخص؟\n\nسيتم:\n- تحديث التقييم الموجود\n- الاحتفاظ بالتقييم السابق كنسخة احتياطية\n- لن يتم تعديل البيانات الشخصية')">
                        <i class="fas fa-edit"></i> تحديث التقييمات ({{ results.category_4|length }})
                    </button>
                </form>
            </div>

            {% for record in results.category_4[:10] %}
            <div class="card mb-2 border-secondary shadow-sm">
                <div class="card-body p-3">
                    <div class="row align-items-center">
                        <div class="col-md-5">
                            <h6 class="text-secondary mb-1">{{ record.name }}</h6>
                            {% if record.was_corrected %}
                            <small class="text-muted">الأصلي: {{ record.original_name }}</small><br>
                            {% endif %}
                            <small class="text-muted">الصف: {{ record.excel_record.row_index }}</small>
                            <br><small class="text-success"><i class="fas fa-check"></i> موجود في القاعدة والدورة</small>
                            <br><small class="text-info"><i class="fas fa-star"></i> لديه تقييم سابق</small>
                        </div>
                        <div class="col-md-3">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">التقييم الحالي:</small>
                                    {% if record.evaluation_record.grade %}
                                    <br><span class="badge bg-light text-dark">{{ record.evaluation_record.grade }}</span>
                                    {% endif %}
                                    {% if record.evaluation_record.percentage %}
                                    <br><span class="badge bg-light text-dark">{{ record.evaluation_record.percentage }}%</span>
                                    {% endif %}
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">التقييم الجديد:</small>
                                    {% if record.excel_record.grade %}
                                    <br><span class="badge bg-primary">{{ record.excel_record.grade }}</span>
                                    {% endif %}
                                    {% if record.excel_record.percentage %}
                                    <br><span class="badge bg-success">{{ record.excel_record.percentage }}%</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-secondary fs-6 p-2">
                                <i class="fas fa-edit"></i> تحديث تقييم
                            </span>
                            <br><small class="text-muted mt-1">استبدال التقييم</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            {% if results.category_4|length > 10 %}
            <div class="text-center mt-3">
                <p class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 سجلات فقط من أصل {{ results.category_4|length }}. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- السجلات المرفوضة -->
    {% if results.blocked_duplicates %}
    <div class="result-section">
        <div class="section-header bg-danger text-white">
            <h4 class="mb-0">
                <i class="fas fa-ban"></i> سجلات مرفوضة - تطابق في البيانات
                <span class="badge bg-light text-danger">{{ results.blocked_duplicates|length }}</span>
            </h4>
            <p class="mb-0 mt-2"><small>هذه السجلات مرفوضة بسبب تطابق في البيانات الشخصية</small></p>
        </div>
        <div class="p-3">
            <div class="alert alert-danger border-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>سبب الرفض:</strong> تطابق في البيانات الشخصية (الرقم الوطني، الهاتف، أو الرقم العسكري) مع أشخاص آخرين
                <br><small class="text-muted">هذه السجلات تحتاج مراجعة يدوية لحل التعارض</small>
            </div>

            {% for record in results.blocked_duplicates[:10] %}
            <div class="card mb-2 border-danger shadow-sm">
                <div class="card-body p-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="text-danger mb-1">{{ record.corrected_name }}</h6>
                            {% if record.excel_record.original_name and record.excel_record.original_name != record.corrected_name %}
                            <small class="text-muted">الأصلي: {{ record.excel_record.original_name }}</small><br>
                            {% endif %}
                            <small class="text-muted">الصف: {{ record.excel_record.row_index }}</small>

                            <div class="mt-2">
                                {% if record.excel_record.national_id %}
                                <span class="badge bg-danger me-1">رقم وطني: {{ record.excel_record.national_id }}</span>
                                {% endif %}
                                {% if record.excel_record.phone %}
                                <span class="badge bg-danger me-1">هاتف: {{ record.excel_record.phone }}</span>
                                {% endif %}
                                {% if record.excel_record.military_id %}
                                <span class="badge bg-danger me-1">عسكري: {{ record.excel_record.military_id }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">سبب الرفض:</small>
                            <br><small class="text-danger">{{ record.reason }}</small>
                            {% if record.db_record %}
                            <br><small class="text-muted">يتطابق مع: {{ record.db_record.name }}</small>
                            {% endif %}
                        </div>
                        <div class="col-md-3 text-end">
                            <span class="badge bg-danger fs-6 p-2">
                                <i class="fas fa-ban"></i> مرفوض
                            </span>
                            <br><small class="text-muted mt-1">يحتاج مراجعة</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            {% if results.blocked_duplicates|length > 10 %}
            <div class="text-center mt-3">
                <p class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 سجلات مرفوضة فقط من أصل {{ results.blocked_duplicates|length }}. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- الأسماء المصححة -->
    {% if results.corrected_names %}
    <div class="result-section">
        <div class="section-header bg-warning text-dark">
            <h4 class="mb-0">
                <i class="fas fa-spell-check"></i> الأسماء المصححة تلقائياً
                <span class="badge bg-dark text-warning">{{ results.corrected_names|length }}</span>
            </h4>
            <p class="mb-0 mt-2"><small>تم تصحيح هذه الأسماء تلقائياً بواسطة النظام الذكي</small></p>
        </div>
        <div class="p-3">
            <div class="alert alert-warning border-warning">
                <i class="fas fa-magic"></i>
                <strong>تصحيح تلقائي:</strong> تم تصحيح الأخطاء الإملائية والتنسيق تلقائياً
                <br><small class="text-muted">يرجى مراجعة التصحيحات للتأكد من صحتها</small>
            </div>

            {% for correction in results.corrected_names[:10] %}
            <div class="card mb-2 border-warning shadow-sm">
                <div class="card-body p-2">
                    <div class="row align-items-center">
                        <div class="col-md-5">
                            <small class="text-muted">الاسم الأصلي:</small>
                            <br><span class="text-muted">{{ correction.original }}</span>
                        </div>
                        <div class="col-md-1 text-center">
                            <i class="fas fa-arrow-left text-warning"></i>
                        </div>
                        <div class="col-md-5">
                            <small class="text-muted">الاسم المصحح:</small>
                            <br><strong class="text-warning">{{ correction.corrected }}</strong>
                        </div>
                        <div class="col-md-1 text-end">
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-check"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            {% if results.corrected_names|length > 10 %}
            <div class="text-center mt-3">
                <p class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 تصحيحات فقط من أصل {{ results.corrected_names|length }}. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Export Section -->
    <div class="mt-5 mb-4">
        <div class="card">
            <div class="card-body">
                <h5 class="text-danger text-center mb-4">
                    <i class="fas fa-star"></i> خيارات التصدير والاستيراد للتقييمات
                </h5>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-danger">
                            <div class="card-body text-center">
                                <i class="fas fa-download text-danger mb-3" style="font-size: 2rem;"></i>
                                <h6>التقرير الكامل للتقييمات</h6>
                                <p class="text-muted small">
                                    <strong>📊 محتوى التقرير:</strong><br>
                                    • <span class="text-success">✅ لديهم تقييم - سيتم التحديث:</span> {{ results.statistics.exact_matches_count }} شخص<br>
                                    • <span class="text-info">➕ ليس لديهم تقييم - سيتم الإضافة:</span> تفصيل كامل<br>
                                    • <span class="text-primary">🆕 غير موجود - سيتم إدخال تقييم لهم:</span> {{ results.statistics.new_records_count }} شخص<br>
                                    • <span class="text-warning">📝 أسماء مصححة:</span> {{ results.statistics.corrected_count }} اسم<br>
                                    • <span class="text-danger">❌ تقييمات مرفوضة:</span> {{ results.statistics.blocked_duplicates_count }} تقييم<br>
                                    • <span class="text-dark">📈 جميع الإحصائيات والتفاصيل</span> في أوراق منفصلة<br>
                                    <strong>📁 إجمالي:</strong> {{ results.statistics.total_processed }} سجل معالج
                                </p>
                                <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-danger">
                                    <i class="fas fa-download"></i> تحميل التقرير الكامل (Excel)
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-file-excel text-success mb-3" style="font-size: 2rem;"></i>
                                <h6>التقييمات الجديدة فقط</h6>
                                <p class="text-muted small">
                                    الأشخاص الذين سيتم إدخال تقييمات لهم<br>
                                    (غير موجودين في قاعدة البيانات)
                                </p>
                                <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-success">
                                    <i class="fas fa-file-excel"></i> تحميل
                                </a>
                            </div>
                        </div>
                    </div>

                    {% if results.new_records or results.allowed_duplicates or results.corrected_names %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-database text-warning mb-3" style="font-size: 2rem;"></i>
                                <h6>إضافة التقييمات الجديدة</h6>
                                <p class="text-muted small">
                                    إضافة الأشخاص الجدد مع تقييماتهم<br>
                                    إلى قاعدة البيانات والدورة
                                </p>
                                <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-warning"
                                            onclick="return confirm('هل تريد إضافة {{ (results.new_records|length if results.new_records else 0) + (results.allowed_duplicates|length if results.allowed_duplicates else 0) + (results.corrected_names|length if results.corrected_names else 0) }} شخص جديد مع تقييماتهم إلى قاعدة البيانات{% if selected_course %} وإلى الدورة {{ selected_course.title }}{% endif %}؟')">
                                        <i class="fas fa-user-plus"></i> إضافة الأشخاص والتقييمات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- إحصائيات التحليل -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="text-danger mb-3 text-center">
                        <i class="fas fa-chart-pie"></i> ملخص تحليل التقييمات بالنسب المئوية
                    </h6>
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-success">{{ "%.1f"|format((results.statistics.new_records_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">سيتم إدخال تقييم لهم</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-info">{{ "%.1f"|format((results.statistics.exact_matches_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">يحتاج تحديث تقييم</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-warning">{{ "%.1f"|format((results.statistics.corrected_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">أسماء مصححة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-danger">{{ "%.1f"|format((results.statistics.blocked_duplicates_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                            <small class="text-muted">تقييمات مرفوضة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div id="evaluationChartsSection" class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);">
                <div class="card-header text-white text-center border-0" style="background: transparent;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div></div>
                        <div class="text-center">
                            <h3 class="mb-0 fw-bold">
                                <i class="fas fa-chart-pie me-2"></i> التقرير الكامل لتحليل التقييمات - الرسوم البيانية
                            </h3>
                            <p class="mb-0 mt-2 opacity-75">تحليل مرئي شامل لنتائج معالجة بيانات التقييمات</p>
                        </div>
                        <div class="dropdown evaluation-export-controls">
                            <button class="btn btn-light btn-sm dropdown-toggle" type="button" id="evaluationExportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-download me-1"></i> تصدير الرسوم
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="evaluationExportDropdown">
                                <li><a class="dropdown-item" href="#" onclick="exportEvaluationChartsToExcel()">
                                    <i class="fas fa-file-excel text-success me-2"></i> تصدير إلى Excel + صورة
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportEvaluationChartsToWord()">
                                    <i class="fas fa-file-word text-primary me-2"></i> تصدير إلى Word
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportEvaluationChartsAsPDF()">
                                    <i class="fas fa-file-pdf text-danger me-2"></i> تصدير إلى PDF
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="exportEvaluationChartsAsImages()">
                                    <i class="fas fa-images text-info me-2"></i> تصدير كصورة PNG
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">

                    <!-- Main Statistics Chart -->
                    <div class="row mb-5">
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-gradient-primary text-white text-center">
                                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع التقييمات الرئيسية</h5>
                                </div>
                                <div class="card-body d-flex align-items-center justify-content-center" style="height: 400px;">
                                    <canvas id="evaluationMainStatsChart" style="max-width: 350px; max-height: 350px;"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-gradient-success text-white text-center">
                                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات التطابق</h5>
                                </div>
                                <div class="card-body d-flex align-items-center justify-content-center" style="height: 400px;">
                                    <canvas id="evaluationMatchChart" style="max-width: 350px; max-height: 350px;"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Bars Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-gradient-info text-white text-center">
                                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>تقدم معالجة التقييمات بالنسب المئوية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="fw-bold text-success mb-2">
                                                <i class="fas fa-check-circle me-1"></i>
                                                يحتاج تحديث تقييم ({{ results.statistics.exact_matches_count }})
                                            </label>
                                            {% set exact_percentage = (results.statistics.exact_matches_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                            <div class="progress mb-2" style="height: 25px;">
                                                <div class="progress-bar bg-success progress-bar-striped progress-bar-animated"
                                                     style="width: {{ exact_percentage }}%">
                                                    {{ "%.1f"|format(exact_percentage) }}%
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="fw-bold text-primary mb-2">
                                                <i class="fas fa-plus-circle me-1"></i>
                                                سيتم إدخال تقييم لهم ({{ results.statistics.new_records_count }})
                                            </label>
                                            {% set new_percentage = (results.statistics.new_records_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                            <div class="progress mb-2" style="height: 25px;">
                                                <div class="progress-bar bg-primary progress-bar-striped progress-bar-animated"
                                                     style="width: {{ new_percentage }}%">
                                                    {{ "%.1f"|format(new_percentage) }}%
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="fw-bold text-warning mb-2">
                                                <i class="fas fa-edit me-1"></i>
                                                أسماء مصححة ({{ results.statistics.corrected_count }})
                                            </label>
                                            {% set corrected_percentage = (results.statistics.corrected_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                            <div class="progress mb-2" style="height: 25px;">
                                                <div class="progress-bar bg-warning progress-bar-striped progress-bar-animated"
                                                     style="width: {{ corrected_percentage }}%">
                                                    {{ "%.1f"|format(corrected_percentage) }}%
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="fw-bold text-danger mb-2">
                                                <i class="fas fa-ban me-1"></i>
                                                تقييمات مرفوضة ({{ results.statistics.blocked_duplicates_count }})
                                            </label>
                                            {% set blocked_eval_percentage = (results.statistics.blocked_duplicates_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                            <div class="progress mb-2" style="height: 25px;">
                                                <div class="progress-bar bg-danger progress-bar-striped progress-bar-animated"
                                                     style="width: {{ blocked_eval_percentage }}%">
                                                    {{ "%.1f"|format(blocked_eval_percentage) }}%
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comparison Chart -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-gradient-warning text-dark text-center">
                                    <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>مقارنة شاملة لنتائج التقييمات</h5>
                                </div>
                                <div class="card-body" style="height: 300px;">
                                    <canvas id="evaluationComparisonChart" style="max-height: 250px;"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

</div>

<!-- Modal تحديث التقييمات -->
{% if results.exact_matches %}
<div class="modal fade" id="updateEvaluationsModal" tabindex="-1" aria-labelledby="updateEvaluationsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="updateEvaluationsModalLabel">
                    <i class="fas fa-edit"></i> تحديث التقييمات المدخلة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> يمكنك تحديث بيانات التقييمات للأشخاص الموجودين في قاعدة البيانات.
                </div>

                <form method="post" action="{{ url_for('person_data.update_evaluations') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الرقم العسكري</th>
                                    <th>الإجراء</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for match in results.exact_matches %}
                                <tr>
                                    <td>
                                        <input type="checkbox" name="selected_evaluations" value="{{ match.db_record.id }}" class="form-check-input evaluation-checkbox">
                                    </td>
                                    <td><strong>{{ match.db_name }}</strong></td>
                                    <td>{{ match.db_record.national_id or '-' }}</td>
                                    <td>{{ match.db_record.phone or '-' }}</td>
                                    <td>{{ match.db_record.military_id or '-' }}</td>
                                    <td>
                                        <select name="action_{{ match.db_record.id }}" class="form-select form-select-sm">
                                            <option value="update_data">تحديث البيانات الشخصية</option>
                                            <option value="add_evaluation">إضافة تقييم جديد</option>
                                            <option value="update_evaluation">تحديث التقييم الحالي</option>
                                        </select>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i> تحديث التقييمات المختارة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تأثيرات بصرية للبطاقات
        $('.stats-card, .result-section').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // إضافة تأثير للأزرار
        $('.btn-export, .btn-export-secondary').hover(
            function() {
                $(this).find('i').addClass('fa-bounce');
            },
            function() {
                $(this).find('i').removeClass('fa-bounce');
            }
        );

        // تحديد الكل في modal التحديث
        $('#selectAll').change(function() {
            $('.evaluation-checkbox').prop('checked', this.checked);
        });

        // تحديث حالة "تحديد الكل" عند تغيير الاختيارات الفردية
        $('.evaluation-checkbox').change(function() {
            var total = $('.evaluation-checkbox').length;
            var checked = $('.evaluation-checkbox:checked').length;
            $('#selectAll').prop('checked', total === checked);
        });
    });
</script>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Export Libraries -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<script>
// تحضير بيانات التقييمات - الفئات الأربعة الجديدة
const evalTotalProcessed = {{ results.statistics.total_processed }};
const evalCategory1 = {{ results.statistics.category_1_count }};  // غير موجود في القاعدة
const evalCategory2 = {{ results.statistics.category_2_count }};  // في القاعدة والدورة بدون تقييم
const evalCategory3 = {{ results.statistics.category_3_count }};  // في القاعدة وليس في الدورة
const evalCategory4 = {{ results.statistics.category_4_count }};  // في القاعدة والدورة ولديه تقييم
const evalCorrectedCount = {{ results.statistics.corrected_count }};
const evalBlockedDuplicates = {{ results.statistics.blocked_duplicates_count }};

// الألوان للفئات الأربعة
const evalColors = {
    category1: '#28a745',    // أخضر - جديد
    category2: '#17a2b8',    // أزرق فاتح - إضافة تقييم
    category3: '#ffc107',    // أصفر - إضافة للدورة
    category4: '#6c757d',    // رمادي - تحديث تقييم
    corrected: '#fd7e14',    // برتقالي - مصحح
    blocked: '#dc3545',      // أحمر - مرفوض
    success: '#28a745',
    info: '#17a2b8',
    warning: '#ffc107',
    secondary: '#6c757d',
    danger: '#dc3545'
};

// الرسم البياني الدائري الرئيسي للفئات الأربعة
const evalMainStatsCtx = document.getElementById('evaluationMainStatsChart').getContext('2d');
new Chart(evalMainStatsCtx, {
    type: 'doughnut',
    data: {
        labels: [
            'الفئة 1: غير موجود في القاعدة',
            'الفئة 2: في القاعدة والدورة بدون تقييم',
            'الفئة 3: في القاعدة وليس في الدورة',
            'الفئة 4: في القاعدة والدورة ولديه تقييم',
            'سجلات مرفوضة'
        ],
        datasets: [{
            data: [evalCategory1, evalCategory2, evalCategory3, evalCategory4, evalBlockedDuplicates],
            backgroundColor: [
                evalColors.category1,
                evalColors.category2,
                evalColors.category3,
                evalColors.category4,
                evalColors.blocked
            ],
            borderWidth: 3,
            borderColor: '#fff',
            hoverBorderWidth: 5
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 1,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    font: {
                        family: 'Cairo',
                        size: 11
                    }
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const percentage = ((context.parsed / evalTotalProcessed) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                    }
                }
            }
        }
    }
});

// رسم بياني شريطي للفئات الأربعة
const evalCategoriesCtx = document.getElementById('evaluationMatchChart').getContext('2d');
new Chart(evalCategoriesCtx, {
    type: 'bar',
    data: {
        labels: [
            'الفئة 1\nغير موجود في القاعدة',
            'الفئة 2\nفي القاعدة والدورة بدون تقييم',
            'الفئة 3\nفي القاعدة وليس في الدورة',
            'الفئة 4\nفي القاعدة والدورة ولديه تقييم',
            'سجلات مرفوضة'
        ],
        datasets: [{
            label: 'عدد السجلات',
            data: [evalCategory1, evalCategory2, evalCategory3, evalCategory4, evalBlockedDuplicates],
            backgroundColor: [
                evalColors.category1,
                evalColors.category2,
                evalColors.category3,
                evalColors.category4,
                evalColors.blocked
            ],
            borderColor: [
                evalColors.category1,
                evalColors.category2,
                evalColors.category3,
                evalColors.category4,
                evalColors.blocked
            ],
            borderWidth: 2,
            borderRadius: 5
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 1,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const percentage = ((context.parsed.y / evalTotalProcessed) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed.y + ' (' + percentage + '%)';
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// رسم بياني خطي للمقارنة بين الفئات
const evalComparisonCtx = document.getElementById('evaluationComparisonChart').getContext('2d');
new Chart(evalComparisonCtx, {
    type: 'line',
    data: {
        labels: ['الفئة 1', 'الفئة 2', 'الفئة 3', 'الفئة 4', 'مرفوضة', 'مصححة'],
        datasets: [{
            label: 'عدد السجلات',
            data: [evalCategory1, evalCategory2, evalCategory3, evalCategory4, evalBlockedDuplicates, evalCorrectedCount],
            borderColor: evalColors.info,
            backgroundColor: evalColors.info + '20',
            borderWidth: 4,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: [
                evalColors.category1,
                evalColors.category2,
                evalColors.category3,
                evalColors.category4,
                evalColors.blocked,
                evalColors.corrected
            ],
            pointBorderColor: '#fff',
            pointBorderWidth: 3,
            pointRadius: 8,
            pointHoverRadius: 12
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 3,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// دوال التصدير للتقييمات
function exportEvaluationChartsToExcel() {
    captureEvaluationChartsSection().then(canvas => {
        // تحويل الصورة إلى base64
        const imageData = canvas.toDataURL('image/png');

        // تحميل الصورة مباشرة
        downloadImage(imageData, 'تقرير_الرسوم_البيانية_التقييمات_' + new Date().toISOString().split('T')[0] + '.png');

        // إنشاء workbook مع البيانات
        const wb = XLSX.utils.book_new();

        // بيانات الإحصائيات
        const data = [
            ['تقرير تحليل التقييمات - الرسوم البيانية'],
            ['تاريخ التقرير: ' + new Date().toLocaleDateString('ar-SA')],
            [''],
            ['الإحصائيات الرئيسية:'],
            ['نوع التقييم', 'العدد', 'النسبة المئوية'],
            ['الفئة 1: غير موجود في القاعدة', evalCategory1, ((evalCategory1 / evalTotalProcessed) * 100).toFixed(1) + '%'],
            ['الفئة 2: في القاعدة والدورة بدون تقييم', evalCategory2, ((evalCategory2 / evalTotalProcessed) * 100).toFixed(1) + '%'],
            ['الفئة 3: في القاعدة وليس في الدورة', evalCategory3, ((evalCategory3 / evalTotalProcessed) * 100).toFixed(1) + '%'],
            ['الفئة 4: في القاعدة والدورة ولديه تقييم', evalCategory4, ((evalCategory4 / evalTotalProcessed) * 100).toFixed(1) + '%'],
            ['أسماء مصححة', evalCorrectedCount, ((evalCorrectedCount / evalTotalProcessed) * 100).toFixed(1) + '%'],
            ['سجلات مرفوضة', evalBlockedDuplicates, ((evalBlockedDuplicates / evalTotalProcessed) * 100).toFixed(1) + '%'],
            ['إجمالي المعالجة', evalTotalProcessed, '100%'],
            [''],
            ['ملخص الإجراءات المطلوبة:'],
            ['نوع الإجراء', 'العدد'],
            ['إنشاء جديد + إضافة للدورة + تقييم', evalCategory1],
            ['إضافة تقييم فقط', evalCategory2],
            ['إضافة للدورة + تقييم', evalCategory3],
            ['تحديث تقييم موجود', evalCategory4],
            [''],
            ['ملاحظة: تم تحميل الرسوم البيانية كصورة منفصلة']
        ];

        const ws = XLSX.utils.aoa_to_sheet(data);

        // تنسيق الأعمدة
        ws['!cols'] = [{wch: 30}, {wch: 15}, {wch: 20}];

        XLSX.utils.book_append_sheet(wb, ws, 'تقرير الرسوم البيانية');

        // تصدير Excel
        XLSX.writeFile(wb, 'تقرير_الرسوم_البيانية_التقييمات_بيانات_' + new Date().toISOString().split('T')[0] + '.xlsx');

        showEvaluationExportSuccess('Excel + صورة PNG');
    });
}

function exportEvaluationChartsToWord() {
    captureEvaluationChartsSection().then(canvas => {
        const imageData = canvas.toDataURL('image/png');

        // إنشاء محتوى HTML مع الصورة
        let htmlContent = `
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; text-align: center; }
                    .header { color: #ff6b6b; margin-bottom: 30px; }
                    .chart-image { max-width: 100%; height: auto; border: 1px solid #ddd; margin: 20px 0; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>تقرير تحليل التقييمات - الرسوم البيانية</h1>
                    <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                <div>
                    <img src="${imageData}" class="chart-image" alt="الرسوم البيانية للتقييمات">
                </div>
            </body>
            </html>
        `;

        // إنشاء blob وتحميله
        const blob = new Blob([htmlContent], { type: 'application/msword' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'تقرير_الرسوم_البيانية_التقييمات_' + new Date().toISOString().split('T')[0] + '.doc';
        a.click();
        URL.revokeObjectURL(url);

        showEvaluationExportSuccess('Word');
    });
}

function exportEvaluationChartsAsPDF() {
    captureEvaluationChartsSection().then(canvas => {
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('l', 'mm', 'a4'); // landscape للحصول على مساحة أكبر

        // إضافة العنوان
        pdf.setFontSize(16);
        pdf.text('تقرير تحليل التقييمات - الرسوم البيانية', 148, 20, { align: 'center' });

        // إضافة التاريخ
        pdf.setFontSize(12);
        pdf.text('تاريخ التقرير: ' + new Date().toLocaleDateString('ar-SA'), 148, 30, { align: 'center' });

        // إضافة الصورة
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 250;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        // التأكد من أن الصورة تدخل في الصفحة
        const maxHeight = 180;
        let finalWidth = imgWidth;
        let finalHeight = imgHeight;

        if (imgHeight > maxHeight) {
            finalHeight = maxHeight;
            finalWidth = (canvas.width * maxHeight) / canvas.height;
        }

        const x = (297 - finalWidth) / 2;
        const y = 40;

        pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);

        // حفظ الملف
        pdf.save('تقرير_الرسوم_البيانية_التقييمات_' + new Date().toISOString().split('T')[0] + '.pdf');

        showEvaluationExportSuccess('PDF');
    });
}

function exportEvaluationChartsAsImages() {
    // تصدير القسم كاملاً كصورة واحدة
    captureEvaluationChartsSection().then(canvas => {
        const imageData = canvas.toDataURL('image/png');
        downloadImage(imageData, 'تقرير_الرسوم_البيانية_التقييمات_كامل_' + new Date().toISOString().split('T')[0] + '.png');
        showEvaluationExportSuccess('صورة كاملة للتقرير');
    });
}

// دالة التقاط قسم الرسوم البيانية للتقييمات كاملاً
function captureEvaluationChartsSection() {
    const element = document.getElementById('evaluationChartsSection');
    const exportControls = element.querySelector('.evaluation-export-controls');

    // إخفاء أدوات التصدير مؤقتاً
    if (exportControls) {
        exportControls.style.visibility = 'hidden';
    }

    // انتظار قصير للتأكد من إخفاء العناصر
    return new Promise(resolve => {
        setTimeout(() => {
            html2canvas(element, {
                useCORS: true,
                allowTaint: true,
                scale: 2, // جودة عالية
                backgroundColor: null, // الحفاظ على الخلفية الأصلية
                width: element.scrollWidth,
                height: element.scrollHeight,
                scrollX: 0,
                scrollY: 0,
                ignoreElements: function(el) {
                    // تجاهل عناصر التصدير
                    return el.classList.contains('evaluation-export-controls') ||
                           el.classList.contains('dropdown') ||
                           el.classList.contains('dropdown-menu') ||
                           el.id === 'evaluationExportDropdown';
                }
            }).then(canvas => {
                // إعادة إظهار أدوات التصدير
                if (exportControls) {
                    exportControls.style.visibility = 'visible';
                }
                resolve(canvas);
            });
        }, 100);
    });
}

function showEvaluationExportSuccess(format) {
    // إنشاء تنبيه نجاح
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        <strong>تم التصدير بنجاح!</strong><br>
        تم تصدير تقرير التقييمات إلى ${format}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
{% endblock %}
