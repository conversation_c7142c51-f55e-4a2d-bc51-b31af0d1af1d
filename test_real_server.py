#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الخادم الحقيقي
"""

import urllib.request
import urllib.error
import time

print("🧪 اختبار الخادم الحقيقي...")

def test_url(url, description):
    """اختبار URL محدد"""
    try:
        print(f"🔍 اختبار {description}: {url}")
        
        # إنشاء طلب HTTP
        req = urllib.request.Request(url)
        req.add_header('User-Agent', 'Test Script')
        
        # إرسال الطلب
        with urllib.request.urlopen(req, timeout=10) as response:
            status_code = response.getcode()
            content = response.read().decode('utf-8')
            
            print(f"   📊 Status Code: {status_code}")
            
            if status_code == 200:
                print(f"   ✅ {description} يعمل بشكل صحيح")
                return True
            else:
                print(f"   ❌ {description} - خطأ {status_code}")
                return False
                
    except urllib.error.HTTPError as e:
        print(f"   ❌ HTTP Error {e.code}: {e.reason}")
        if e.code == 500:
            # قراءة تفاصيل الخطأ
            error_content = e.read().decode('utf-8')
            if 'BuildError' in error_content:
                print("   🔍 تم اكتشاف BuildError في الخادم الحقيقي!")
            print(f"   📄 تفاصيل الخطأ: {error_content[:200]}...")
        return False
    except urllib.error.URLError as e:
        print(f"   ❌ URL Error: {e.reason}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ عام: {e}")
        return False

# انتظار قليل للتأكد من أن الخادم جاهز
print("⏳ انتظار تشغيل الخادم...")
time.sleep(3)

# اختبار الصفحات
urls_to_test = [
    ('http://localhost:5000/', 'الصفحة الرئيسية'),
    ('http://localhost:5000/login', 'صفحة تسجيل الدخول'),
    ('http://localhost:5000/dashboard', 'لوحة التحكم'),
]

print("\n📋 نتائج الاختبار:")
print("=" * 50)

success_count = 0
total_count = len(urls_to_test)

for url, description in urls_to_test:
    if test_url(url, description):
        success_count += 1
    print()

print("=" * 50)
print(f"📊 النتيجة النهائية: {success_count}/{total_count} صفحة تعمل")

if success_count == total_count:
    print("🎉 جميع الصفحات تعمل بشكل مثالي!")
elif success_count >= total_count * 0.8:
    print("✅ معظم الصفحات تعمل بشكل جيد")
else:
    print("⚠️ هناك مشاكل في عدة صفحات")

print("\n🏁 انتهى اختبار الخادم الحقيقي")
