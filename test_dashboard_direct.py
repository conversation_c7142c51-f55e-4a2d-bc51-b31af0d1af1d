#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مباشر لصفحة Dashboard
"""

import urllib.request
import urllib.parse
import http.cookiejar
import time

print("🧪 اختبار مباشر لصفحة Dashboard...")

def test_dashboard():
    """اختبار صفحة Dashboard مع تسجيل دخول"""
    
    # إنشاء cookie jar لحفظ الجلسة
    cookie_jar = http.cookiejar.CookieJar()
    opener = urllib.request.build_opener(urllib.request.HTTPCookieProcessor(cookie_jar))
    
    try:
        print("🔐 محاولة تسجيل الدخول...")
        
        # الحصول على صفحة تسجيل الدخول أولاً
        login_url = 'http://localhost:5000/login'
        req = urllib.request.Request(login_url)
        req.add_header('User-Agent', 'Test Script')
        
        with opener.open(req) as response:
            login_page = response.read().decode('utf-8')
            print("✅ تم الحصول على صفحة تسجيل الدخول")
        
        # استخراج CSRF token (بسيط)
        csrf_token = None
        for line in login_page.split('\n'):
            if 'csrf_token' in line and 'value=' in line:
                start = line.find('value="') + 7
                end = line.find('"', start)
                csrf_token = line[start:end]
                break
        
        if not csrf_token:
            print("⚠️ لم يتم العثور على CSRF token، سأحاول بدونه")
            csrf_token = ''
        
        # بيانات تسجيل الدخول
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': csrf_token,
            'submit': 'تسجيل الدخول'
        }
        
        # تحويل البيانات إلى URL encoded
        data = urllib.parse.urlencode(login_data).encode('utf-8')
        
        # إرسال طلب تسجيل الدخول
        req = urllib.request.Request(login_url, data=data)
        req.add_header('Content-Type', 'application/x-www-form-urlencoded')
        req.add_header('User-Agent', 'Test Script')
        
        with opener.open(req) as response:
            login_response = response.read().decode('utf-8')
            print(f"📊 Login Status Code: {response.getcode()}")
            
            # التحقق من نجاح تسجيل الدخول
            if 'dashboard' in response.geturl() or response.getcode() == 302:
                print("✅ تم تسجيل الدخول بنجاح")
            else:
                print("⚠️ قد يكون هناك مشكلة في تسجيل الدخول")
        
        print("\n🏠 اختبار صفحة Dashboard...")
        
        # اختبار صفحة Dashboard
        dashboard_url = 'http://localhost:5000/dashboard'
        req = urllib.request.Request(dashboard_url)
        req.add_header('User-Agent', 'Test Script')
        
        with opener.open(req) as response:
            dashboard_content = response.read().decode('utf-8')
            status_code = response.getcode()
            
            print(f"📊 Dashboard Status Code: {status_code}")
            
            if status_code == 200:
                print("✅ صفحة Dashboard تعمل بشكل صحيح")
                
                # البحث عن علامات النجاح
                if 'لوحة التحكم' in dashboard_content:
                    print("✅ تم العثور على محتوى لوحة التحكم")
                if 'إدارة المواد الدراسية' in dashboard_content:
                    print("✅ تم العثور على رابط إدارة المواد")
                else:
                    print("⚠️ لم يتم العثور على رابط إدارة المواد")
                
                return True
            else:
                print(f"❌ خطأ في Dashboard - Status Code: {status_code}")
                return False
                
    except urllib.error.HTTPError as e:
        print(f"❌ HTTP Error {e.code}: {e.reason}")
        if e.code == 500:
            error_content = e.read().decode('utf-8')
            print(f"📄 تفاصيل الخطأ: {error_content[:300]}...")
            
            if 'BuildError' in error_content:
                print("🔍 تم اكتشاف BuildError!")
                if 'subjects_index' in error_content:
                    print("❌ المشكلة في subjects_index - لم يتم تحميل مسارات المواد!")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

# تشغيل الاختبار
print("⏳ انتظار تشغيل الخادم...")
time.sleep(2)

if test_dashboard():
    print("\n🎉 اختبار Dashboard نجح!")
else:
    print("\n❌ اختبار Dashboard فشل!")

print("\n🏁 انتهى الاختبار المباشر")
