#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مسارات نظام إدارة المواد الدراسية
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, send_file, abort
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from sqlalchemy import text, or_, and_, func, desc, asc
from datetime import datetime, timezone
import os
import json

from app import app, db
from app import (Subject, CourseSubject, SubjectCategory, SubjectLevel, 
                SubjectRating, SubjectUsageStats, Course, User,
                SubjectForm, EditSubjectForm, CourseSubjectForm, 
                SubjectCategoryForm, SubjectLevelForm, SubjectRatingForm, SubjectSearchForm)

# الصفحة الرئيسية لإدارة المواد
@app.route('/subjects')
@login_required
def subjects_index():
    """الصفحة الرئيسية لعرض جميع المواد"""
    
    # نموذج البحث
    search_form = SubjectSearchForm()
    
    # تحديث خيارات القوائم المنسدلة
    categories = SubjectCategory.query.filter_by(is_active=True).all()
    levels = SubjectLevel.query.filter_by(is_active=True).order_by(SubjectLevel.order_index).all()
    
    search_form.category.choices = [('', 'جميع الفئات')] + [(c.name, c.name) for c in categories]
    search_form.level.choices = [('', 'جميع المستويات')] + [(l.name, l.name) for l in levels]
    
    # بناء الاستعلام
    query = Subject.query.filter_by(is_active=True)
    
    # تطبيق فلاتر البحث
    if request.args.get('search_query'):
        search_query = request.args.get('search_query')
        query = query.filter(or_(
            Subject.name.contains(search_query),
            Subject.description.contains(search_query),
            Subject.tags.contains(search_query)
        ))
        search_form.search_query.data = search_query
    
    if request.args.get('category'):
        category = request.args.get('category')
        query = query.filter(Subject.category == category)
        search_form.category.data = category
    
    if request.args.get('level'):
        level = request.args.get('level')
        query = query.filter(Subject.level == level)
        search_form.level.data = level
    
    # تطبيق الترتيب
    sort_by = request.args.get('sort_by', 'name')
    sort_order = request.args.get('sort_order', 'asc')
    
    search_form.sort_by.data = sort_by
    search_form.sort_order.data = sort_order
    
    if sort_by == 'average_rating':
        # ترتيب حسب التقييم (من جدول الإحصائيات)
        query = query.outerjoin(SubjectUsageStats).order_by(
            desc(SubjectUsageStats.average_rating) if sort_order == 'desc' else asc(SubjectUsageStats.average_rating)
        )
    elif sort_by == 'total_courses':
        # ترتيب حسب عدد الدورات
        query = query.outerjoin(SubjectUsageStats).order_by(
            desc(SubjectUsageStats.total_courses) if sort_order == 'desc' else asc(SubjectUsageStats.total_courses)
        )
    elif sort_by == 'total_views':
        # ترتيب حسب عدد المشاهدات
        query = query.outerjoin(SubjectUsageStats).order_by(
            desc(SubjectUsageStats.total_views) if sort_order == 'desc' else asc(SubjectUsageStats.total_views)
        )
    elif sort_by == 'created_at':
        # ترتيب حسب تاريخ الإنشاء
        query = query.order_by(
            desc(Subject.created_at) if sort_order == 'desc' else asc(Subject.created_at)
        )
    else:
        # ترتيب حسب الاسم (افتراضي)
        query = query.order_by(
            desc(Subject.name) if sort_order == 'desc' else asc(Subject.name)
        )
    
    # تطبيق التصفح
    page = request.args.get('page', 1, type=int)
    per_page = 12  # عدد المواد في كل صفحة
    
    subjects = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # جلب الإحصائيات العامة
    total_subjects = Subject.query.filter_by(is_active=True).count()
    total_categories = SubjectCategory.query.filter_by(is_active=True).count()
    total_levels = SubjectLevel.query.filter_by(is_active=True).count()
    
    # إحصائيات الاستخدام
    usage_stats = db.session.query(
        func.sum(SubjectUsageStats.total_courses).label('total_courses'),
        func.sum(SubjectUsageStats.total_participants).label('total_participants'),
        func.sum(SubjectUsageStats.total_downloads).label('total_downloads'),
        func.avg(SubjectUsageStats.average_rating).label('avg_rating')
    ).first()
    
    return render_template('subjects/index.html',
                         title='إدارة المواد الدراسية',
                         subjects=subjects,
                         search_form=search_form,
                         categories=categories,
                         levels=levels,
                         stats={
                             'total_subjects': total_subjects,
                             'total_categories': total_categories,
                             'total_levels': total_levels,
                             'total_courses': usage_stats.total_courses or 0,
                             'total_participants': usage_stats.total_participants or 0,
                             'total_downloads': usage_stats.total_downloads or 0,
                             'avg_rating': round(usage_stats.avg_rating or 0, 1)
                         })

# إضافة مادة جديدة
@app.route('/subjects/add', methods=['GET', 'POST'])
@login_required
def add_subject():
    """إضافة مادة دراسية جديدة"""
    
    form = SubjectForm()
    
    # تحديث خيارات القوائم المنسدلة
    categories = SubjectCategory.query.filter_by(is_active=True).all()
    levels = SubjectLevel.query.filter_by(is_active=True).order_by(SubjectLevel.order_index).all()
    
    form.category.choices = [(c.name, c.name) for c in categories]
    form.level.choices = [(l.name, l.name) for l in levels]
    
    if form.validate_on_submit():
        try:
            # التحقق من عدم تكرار اسم المادة
            existing_subject = Subject.query.filter_by(name=form.name.data).first()
            if existing_subject:
                flash('اسم المادة موجود مسبقاً. يرجى اختيار اسم آخر.', 'danger')
                return render_template('subjects/add.html', title='إضافة مادة جديدة', form=form)
            
            # رفع الملف إذا تم اختياره
            file_path = None
            file_type = None
            file_size = None
            
            if form.file.data:
                file = form.file.data
                filename = secure_filename(file.filename)
                
                # إنشاء اسم ملف فريد
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"subject_{timestamp}_{filename}"
                
                # تحديد مجلد الحفظ
                upload_folder = os.path.join(app.config['UPLOAD_FOLDER'], 'subjects')
                os.makedirs(upload_folder, exist_ok=True)
                
                file_path = os.path.join('subjects', filename)
                full_path = os.path.join(app.config['UPLOAD_FOLDER'], file_path)
                
                file.save(full_path)
                
                # تحديد نوع الملف وحجمه
                file_type = filename.split('.')[-1].lower()
                file_size = os.path.getsize(full_path)
            
            # إنشاء المادة الجديدة
            subject = Subject(
                name=form.name.data,
                description=form.description.data,
                category=form.category.data,
                level=form.level.data,
                duration_hours=form.duration_hours.data,
                file_path=file_path,
                file_type=file_type,
                file_size=file_size,
                preview_content=form.preview_content.data,
                tags=form.tags.data,
                created_by=current_user.id
            )
            
            db.session.add(subject)
            db.session.commit()
            
            # إنشاء سجل إحصائيات للمادة
            stats = SubjectUsageStats(subject_id=subject.id)
            db.session.add(stats)
            db.session.commit()
            
            flash('تم إضافة المادة بنجاح!', 'success')
            return redirect(url_for('subjects_index'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة المادة: {str(e)}', 'danger')
    
    return render_template('subjects/add.html', title='إضافة مادة جديدة', form=form)

# عرض تفاصيل المادة
@app.route('/subjects/<int:subject_id>')
@login_required
def subject_details(subject_id):
    """عرض تفاصيل المادة وارتباطاتها"""
    
    subject = Subject.query.get_or_404(subject_id)
    
    # تحديث عدد المشاهدات
    if subject.usage_stats:
        subject.usage_stats.total_views += 1
        db.session.commit()
    
    # جلب الدورات المرتبطة بالمادة
    course_subjects = db.session.query(CourseSubject, Course).join(
        Course, CourseSubject.course_id == Course.id
    ).filter(CourseSubject.subject_id == subject_id).all()
    
    # جلب التقييمات
    ratings = SubjectRating.query.filter_by(subject_id=subject_id).order_by(
        desc(SubjectRating.created_at)
    ).limit(10).all()
    
    # حساب متوسط التقييم
    avg_rating = db.session.query(func.avg(SubjectRating.rating)).filter_by(
        subject_id=subject_id
    ).scalar() or 0
    
    # التحقق من تقييم المستخدم الحالي
    user_rating = SubjectRating.query.filter_by(
        subject_id=subject_id, user_id=current_user.id
    ).first()
    
    # نموذج التقييم
    rating_form = SubjectRatingForm()
    if user_rating:
        rating_form.rating.data = user_rating.rating
        rating_form.comment.data = user_rating.comment
    
    return render_template('subjects/details.html',
                         title=f'تفاصيل المادة: {subject.name}',
                         subject=subject,
                         course_subjects=course_subjects,
                         ratings=ratings,
                         avg_rating=round(avg_rating, 1),
                         user_rating=user_rating,
                         rating_form=rating_form)

# تحديث المادة
@app.route('/subjects/<int:subject_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_subject(subject_id):
    """تحديث بيانات المادة"""
    
    subject = Subject.query.get_or_404(subject_id)
    
    # التحقق من الصلاحيات
    if current_user.role != 'admin' and current_user.id != subject.created_by:
        flash('ليس لديك صلاحية لتعديل هذه المادة', 'danger')
        return redirect(url_for('subject_details', subject_id=subject_id))
    
    form = EditSubjectForm()
    
    # تحديث خيارات القوائم المنسدلة
    categories = SubjectCategory.query.filter_by(is_active=True).all()
    levels = SubjectLevel.query.filter_by(is_active=True).order_by(SubjectLevel.order_index).all()
    
    form.category.choices = [(c.name, c.name) for c in categories]
    form.level.choices = [(l.name, l.name) for l in levels]
    
    if form.validate_on_submit():
        try:
            # التحقق من عدم تكرار اسم المادة (باستثناء المادة الحالية)
            existing_subject = Subject.query.filter(
                and_(Subject.name == form.name.data, Subject.id != subject_id)
            ).first()
            if existing_subject:
                flash('اسم المادة موجود مسبقاً. يرجى اختيار اسم آخر.', 'danger')
                return render_template('subjects/edit.html', title='تحديث المادة', form=form, subject=subject)
            
            # تحديث بيانات المادة
            subject.name = form.name.data
            subject.description = form.description.data
            subject.category = form.category.data
            subject.level = form.level.data
            subject.duration_hours = form.duration_hours.data
            subject.preview_content = form.preview_content.data
            subject.tags = form.tags.data
            subject.is_active = form.is_active.data
            subject.updated_at = datetime.now(timezone.utc)
            
            # رفع ملف جديد إذا تم اختياره
            if form.file.data:
                # حذف الملف القديم
                if subject.file_path:
                    old_file_path = os.path.join(app.config['UPLOAD_FOLDER'], subject.file_path)
                    if os.path.exists(old_file_path):
                        os.remove(old_file_path)
                
                file = form.file.data
                filename = secure_filename(file.filename)
                
                # إنشاء اسم ملف فريد
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"subject_{timestamp}_{filename}"
                
                # تحديد مجلد الحفظ
                upload_folder = os.path.join(app.config['UPLOAD_FOLDER'], 'subjects')
                os.makedirs(upload_folder, exist_ok=True)
                
                file_path = os.path.join('subjects', filename)
                full_path = os.path.join(app.config['UPLOAD_FOLDER'], file_path)
                
                file.save(full_path)
                
                # تحديث بيانات الملف
                subject.file_path = file_path
                subject.file_type = filename.split('.')[-1].lower()
                subject.file_size = os.path.getsize(full_path)
            
            db.session.commit()
            flash('تم تحديث المادة بنجاح!', 'success')
            return redirect(url_for('subject_details', subject_id=subject_id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث المادة: {str(e)}', 'danger')
    
    # ملء النموذج بالبيانات الحالية
    if request.method == 'GET':
        form.name.data = subject.name
        form.description.data = subject.description
        form.category.data = subject.category
        form.level.data = subject.level
        form.duration_hours.data = subject.duration_hours
        form.preview_content.data = subject.preview_content
        form.tags.data = subject.tags
        form.is_active.data = subject.is_active
    
    return render_template('subjects/edit.html', title='تحديث المادة', form=form, subject=subject)

# حذف المادة
@app.route('/subjects/<int:subject_id>/delete')
@login_required
def delete_subject(subject_id):
    """حذف المادة"""

    subject = Subject.query.get_or_404(subject_id)

    # التحقق من الصلاحيات
    if current_user.role != 'admin' and current_user.id != subject.created_by:
        flash('ليس لديك صلاحية لحذف هذه المادة', 'danger')
        return redirect(url_for('subject_details', subject_id=subject_id))

    # التحقق من عدم ارتباط المادة بأي دورة
    course_count = CourseSubject.query.filter_by(subject_id=subject_id).count()
    if course_count > 0:
        flash(f'لا يمكن حذف هذه المادة لأنها مرتبطة بـ {course_count} دورة. يرجى إزالتها من الدورات أولاً.', 'danger')
        return redirect(url_for('subject_details', subject_id=subject_id))

    try:
        # حذف الملف المرفق
        if subject.file_path:
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], subject.file_path)
            if os.path.exists(file_path):
                os.remove(file_path)

        # حذف المادة (سيتم حذف الإحصائيات والتقييمات تلقائياً بسبب cascade)
        db.session.delete(subject)
        db.session.commit()

        flash('تم حذف المادة بنجاح!', 'success')
        return redirect(url_for('subjects_index'))

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المادة: {str(e)}', 'danger')
        return redirect(url_for('subject_details', subject_id=subject_id))

# تحميل ملف المادة
@app.route('/subjects/<int:subject_id>/download')
@login_required
def download_subject_file(subject_id):
    """تحميل ملف المادة"""

    subject = Subject.query.get_or_404(subject_id)

    if not subject.file_path:
        flash('لا يوجد ملف مرفق بهذه المادة', 'warning')
        return redirect(url_for('subject_details', subject_id=subject_id))

    file_path = os.path.join(app.config['UPLOAD_FOLDER'], subject.file_path)

    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('subject_details', subject_id=subject_id))

    # تحديث عدد التحميلات
    if subject.usage_stats:
        subject.usage_stats.total_downloads += 1
        db.session.commit()

    return send_file(file_path, as_attachment=True, download_name=f"{subject.name}.{subject.file_type}")

# إضافة تقييم للمادة
@app.route('/subjects/<int:subject_id>/rate', methods=['POST'])
@login_required
def rate_subject(subject_id):
    """إضافة أو تحديث تقييم المادة"""

    subject = Subject.query.get_or_404(subject_id)
    form = SubjectRatingForm()

    if form.validate_on_submit():
        try:
            # البحث عن تقييم سابق للمستخدم
            existing_rating = SubjectRating.query.filter_by(
                subject_id=subject_id, user_id=current_user.id
            ).first()

            if existing_rating:
                # تحديث التقييم الموجود
                existing_rating.rating = form.rating.data
                existing_rating.comment = form.comment.data
                flash('تم تحديث تقييمك بنجاح!', 'success')
            else:
                # إضافة تقييم جديد
                rating = SubjectRating(
                    subject_id=subject_id,
                    user_id=current_user.id,
                    rating=form.rating.data,
                    comment=form.comment.data
                )
                db.session.add(rating)
                flash('تم إضافة تقييمك بنجاح!', 'success')

            db.session.commit()

            # تحديث متوسط التقييم في الإحصائيات
            avg_rating = db.session.query(func.avg(SubjectRating.rating)).filter_by(
                subject_id=subject_id
            ).scalar() or 0

            if subject.usage_stats:
                subject.usage_stats.average_rating = avg_rating
                db.session.commit()

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة التقييم: {str(e)}', 'danger')

    return redirect(url_for('subject_details', subject_id=subject_id))
