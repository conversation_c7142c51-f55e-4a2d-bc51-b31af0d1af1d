#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة المواد الدراسية المتكامل
إنشاء جداول المواد والربط مع الدورات والجدولة
"""

from app import app, db
from sqlalchemy import text

def create_subjects_system():
    """إنشاء نظام إدارة المواد الدراسية"""
    
    with app.app_context():
        print("🚀 بدء إنشاء نظام إدارة المواد الدراسية...")
        
        # جدول المواد الرئيسي (منفصل عن الدورات)
        print("📚 إنشاء جدول المواد الرئيسي...")
        db.engine.execute(text("""
        CREATE TABLE IF NOT EXISTS subjects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(200) NOT NULL UNIQUE,
            description TEXT,
            category VARCHAR(100),
            level VARCHAR(50),
            duration_hours INTEGER DEFAULT 1,
            file_path VARCHAR(255),
            file_type VARCHAR(20),
            file_size INTEGER,
            preview_content TEXT,
            tags VARCHAR(500),
            is_active BOOLEAN DEFAULT 1,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES user (id)
        )
        """))
        
        # جدول ربط المواد بالدورات مع التوقيت
        print("🔗 إنشاء جدول ربط المواد بالدورات...")
        db.engine.execute(text("""
        CREATE TABLE IF NOT EXISTS course_subjects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_id INTEGER NOT NULL,
            subject_id INTEGER NOT NULL,
            day_number INTEGER NOT NULL,
            start_time VARCHAR(5) NOT NULL,
            end_time VARCHAR(5) NOT NULL,
            order_index INTEGER DEFAULT 0,
            notes TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES course (id) ON DELETE CASCADE,
            FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE,
            UNIQUE(course_id, subject_id, day_number, start_time)
        )
        """))
        
        # جدول فئات المواد
        print("📂 إنشاء جدول فئات المواد...")
        db.engine.execute(text("""
        CREATE TABLE IF NOT EXISTS subject_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            color VARCHAR(7) DEFAULT '#007bff',
            icon VARCHAR(50) DEFAULT 'fas fa-book',
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # جدول مستويات المواد
        print("📊 إنشاء جدول مستويات المواد...")
        db.engine.execute(text("""
        CREATE TABLE IF NOT EXISTS subject_levels (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) NOT NULL UNIQUE,
            description TEXT,
            order_index INTEGER DEFAULT 0,
            color VARCHAR(7) DEFAULT '#28a745',
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # جدول تقييمات المواد
        print("⭐ إنشاء جدول تقييمات المواد...")
        db.engine.execute(text("""
        CREATE TABLE IF NOT EXISTS subject_ratings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subject_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            rating INTEGER CHECK(rating >= 1 AND rating <= 5),
            comment TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
            UNIQUE(subject_id, user_id)
        )
        """))
        
        # جدول إحصائيات استخدام المواد
        print("📈 إنشاء جدول إحصائيات المواد...")
        db.engine.execute(text("""
        CREATE TABLE IF NOT EXISTS subject_usage_stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subject_id INTEGER NOT NULL,
            total_courses INTEGER DEFAULT 0,
            total_participants INTEGER DEFAULT 0,
            total_downloads INTEGER DEFAULT 0,
            total_views INTEGER DEFAULT 0,
            average_rating FLOAT DEFAULT 0.0,
            last_used_date DATETIME,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE,
            UNIQUE(subject_id)
        )
        """))
        
        print("✅ تم إنشاء جداول نظام المواد بنجاح")
        
        # إضافة فئات المواد الأساسية
        print("📝 إضافة فئات المواد الأساسية...")
        categories = [
            ('البرمجة والتطوير', 'مواد متعلقة بالبرمجة وتطوير البرمجيات', '#007bff', 'fas fa-code'),
            ('إدارة الأعمال', 'مواد إدارة الأعمال والقيادة', '#28a745', 'fas fa-briefcase'),
            ('التسويق والمبيعات', 'مواد التسويق الرقمي والمبيعات', '#ffc107', 'fas fa-chart-line'),
            ('التصميم والجرافيك', 'مواد التصميم والفنون البصرية', '#e83e8c', 'fas fa-palette'),
            ('اللغات', 'مواد تعلم اللغات المختلفة', '#6f42c1', 'fas fa-language'),
            ('العلوم والرياضيات', 'مواد العلوم والرياضيات', '#20c997', 'fas fa-calculator'),
            ('التكنولوجيا والهندسة', 'مواد التكنولوجيا والهندسة', '#fd7e14', 'fas fa-cogs'),
            ('الصحة والطب', 'مواد طبية وصحية', '#dc3545', 'fas fa-heartbeat'),
            ('القانون والمحاسبة', 'مواد قانونية ومحاسبية', '#6c757d', 'fas fa-balance-scale'),
            ('التطوير الشخصي', 'مواد التطوير الذاتي والمهارات الشخصية', '#17a2b8', 'fas fa-user-graduate')
        ]
        
        for name, desc, color, icon in categories:
            try:
                db.engine.execute(text("""
                INSERT OR IGNORE INTO subject_categories (name, description, color, icon)
                VALUES (:name, :desc, :color, :icon)
                """), {'name': name, 'desc': desc, 'color': color, 'icon': icon})
            except Exception as e:
                print(f"تحذير: لم يتم إضافة الفئة {name}: {e}")
        
        # إضافة مستويات المواد الأساسية
        print("📊 إضافة مستويات المواد الأساسية...")
        levels = [
            ('مبتدئ', 'مستوى أساسي للمبتدئين', 1, '#28a745'),
            ('متوسط', 'مستوى متوسط', 2, '#ffc107'),
            ('متقدم', 'مستوى متقدم', 3, '#fd7e14'),
            ('خبير', 'مستوى خبير ومتخصص', 4, '#dc3545'),
            ('احترافي', 'مستوى احترافي عالي', 5, '#6f42c1')
        ]
        
        for name, desc, order, color in levels:
            try:
                db.engine.execute(text("""
                INSERT OR IGNORE INTO subject_levels (name, description, order_index, color)
                VALUES (:name, :desc, :order, :color)
                """), {'name': name, 'desc': desc, 'order': order, 'color': color})
            except Exception as e:
                print(f"تحذير: لم يتم إضافة المستوى {name}: {e}")
        
        # إضافة مواد تجريبية
        print("📚 إضافة مواد تجريبية...")
        sample_subjects = [
            ('مقدمة في البرمجة', 'أساسيات البرمجة للمبتدئين', 'البرمجة والتطوير', 'مبتدئ', 2),
            ('إدارة المشاريع', 'أساسيات إدارة المشاريع الناجحة', 'إدارة الأعمال', 'متوسط', 3),
            ('التسويق الرقمي', 'استراتيجيات التسويق الرقمي الحديثة', 'التسويق والمبيعات', 'متوسط', 4),
            ('تصميم الجرافيك', 'أساسيات التصميم الجرافيكي', 'التصميم والجرافيك', 'مبتدئ', 3),
            ('اللغة الإنجليزية للأعمال', 'تطوير مهارات اللغة الإنجليزية في بيئة العمل', 'اللغات', 'متوسط', 2)
        ]
        
        for name, desc, category, level, duration in sample_subjects:
            try:
                db.engine.execute(text("""
                INSERT OR IGNORE INTO subjects (name, description, category, level, duration_hours)
                VALUES (:name, :desc, :category, :level, :duration)
                """), {'name': name, 'desc': desc, 'category': category, 'level': level, 'duration': duration})
            except Exception as e:
                print(f"تحذير: لم يتم إضافة المادة {name}: {e}")
        
        db.session.commit()
        print("🎉 تم إنشاء نظام إدارة المواد بنجاح!")
        print("📋 تم إضافة:")
        print("   - 10 فئات للمواد")
        print("   - 5 مستويات للمواد") 
        print("   - 5 مواد تجريبية")
        print("   - جداول الربط والإحصائيات")

if __name__ == '__main__':
    create_subjects_system()
