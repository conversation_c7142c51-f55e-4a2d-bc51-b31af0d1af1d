#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مسارات ربط المواد بالدورات والجدولة
"""

from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from sqlalchemy import text, and_, or_, func, desc, asc
from datetime import datetime, timezone, time
import json

from app import app, db
from app import (Subject, CourseSubject, Course, SubjectUsageStats, CourseParticipant,
                CourseSubjectForm, SubjectSearchForm)

# إدارة مواد الدورة
@app.route('/course/<int:course_id>/subjects')
@login_required
def course_subjects_management(course_id):
    """إدارة مواد الدورة والجدولة"""
    
    course = Course.query.get_or_404(course_id)
    
    # التحقق من الصلاحيات
    if current_user.role != 'admin' and current_user.id != course.trainer_id:
        flash('ليس لديك صلاحية لإدارة مواد هذه الدورة', 'danger')
        return redirect(url_for('course_details', course_id=course_id))
    
    # جلب المواد المرتبطة بالدورة مرتبة حسب اليوم والوقت
    course_subjects = db.session.query(CourseSubject, Subject).join(
        Subject, CourseSubject.subject_id == Subject.id
    ).filter(CourseSubject.course_id == course_id).order_by(
        CourseSubject.day_number, CourseSubject.start_time, CourseSubject.order_index
    ).all()
    
    # تنظيم المواد حسب اليوم
    subjects_by_day = {}
    for day in range(1, course.duration_days + 1):
        subjects_by_day[day] = [
            (cs, subject) for cs, subject in course_subjects 
            if cs.day_number == day
        ]
    
    # نموذج إضافة مادة جديدة
    form = CourseSubjectForm()
    
    # تحديث خيارات القوائم المنسدلة
    available_subjects = Subject.query.filter_by(is_active=True).order_by(Subject.name).all()
    form.subject_id.choices = [(s.id, f"{s.name} ({s.category} - {s.level})") for s in available_subjects]
    form.day_number.choices = [(i, f'اليوم {i}') for i in range(1, course.duration_days + 1)]
    
    # إحصائيات الدورة
    total_subjects = len(course_subjects)
    total_hours = sum(cs.subject.duration_hours for cs, subject in course_subjects)
    
    # جلب المواد المتاحة للإضافة (غير مرتبطة بالدورة)
    used_subject_ids = [cs.subject_id for cs, _ in course_subjects]
    available_for_add = Subject.query.filter(
        and_(Subject.is_active == True, ~Subject.id.in_(used_subject_ids))
    ).order_by(Subject.name).all()
    
    return render_template('course_subjects/management.html',
                         title=f'إدارة مواد الدورة: {course.title}',
                         course=course,
                         subjects_by_day=subjects_by_day,
                         form=form,
                         available_for_add=available_for_add,
                         stats={
                             'total_subjects': total_subjects,
                             'total_hours': total_hours,
                             'available_count': len(available_for_add)
                         })

# إضافة مادة للدورة
@app.route('/course/<int:course_id>/subjects/add', methods=['POST'])
@login_required
def add_subject_to_course(course_id):
    """إضافة مادة للدورة مع التوقيت"""
    
    course = Course.query.get_or_404(course_id)
    
    # التحقق من الصلاحيات
    if current_user.role != 'admin' and current_user.id != course.trainer_id:
        flash('ليس لديك صلاحية لإضافة مواد لهذه الدورة', 'danger')
        return redirect(url_for('course_details', course_id=course_id))
    
    form = CourseSubjectForm()
    
    # تحديث خيارات القوائم المنسدلة
    available_subjects = Subject.query.filter_by(is_active=True).order_by(Subject.name).all()
    form.subject_id.choices = [(s.id, f"{s.name} ({s.category} - {s.level})") for s in available_subjects]
    form.day_number.choices = [(i, f'اليوم {i}') for i in range(1, course.duration_days + 1)]
    
    if form.validate_on_submit():
        try:
            # التحقق من عدم تكرار المادة في نفس اليوم والوقت
            existing = CourseSubject.query.filter_by(
                course_id=course_id,
                subject_id=form.subject_id.data,
                day_number=form.day_number.data,
                start_time=form.start_time.data
            ).first()
            
            if existing:
                flash('هذه المادة موجودة مسبقاً في نفس اليوم والوقت', 'danger')
                return redirect(url_for('course_subjects_management', course_id=course_id))
            
            # التحقق من صحة التوقيت
            try:
                start_time = datetime.strptime(form.start_time.data, '%H:%M').time()
                end_time = datetime.strptime(form.end_time.data, '%H:%M').time()
                
                if start_time >= end_time:
                    flash('وقت البدء يجب أن يكون قبل وقت الانتهاء', 'danger')
                    return redirect(url_for('course_subjects_management', course_id=course_id))
                    
            except ValueError:
                flash('تنسيق الوقت غير صحيح. استخدم تنسيق HH:MM', 'danger')
                return redirect(url_for('course_subjects_management', course_id=course_id))
            
            # التحقق من عدم تداخل الأوقات في نفس اليوم
            overlapping = CourseSubject.query.filter(
                and_(
                    CourseSubject.course_id == course_id,
                    CourseSubject.day_number == form.day_number.data,
                    or_(
                        and_(CourseSubject.start_time <= form.start_time.data, CourseSubject.end_time > form.start_time.data),
                        and_(CourseSubject.start_time < form.end_time.data, CourseSubject.end_time >= form.end_time.data),
                        and_(CourseSubject.start_time >= form.start_time.data, CourseSubject.end_time <= form.end_time.data)
                    )
                )
            ).first()
            
            if overlapping:
                flash('يوجد تداخل في الأوقات مع مادة أخرى في نفس اليوم', 'danger')
                return redirect(url_for('course_subjects_management', course_id=course_id))
            
            # إضافة المادة للدورة
            course_subject = CourseSubject(
                course_id=course_id,
                subject_id=form.subject_id.data,
                day_number=form.day_number.data,
                start_time=form.start_time.data,
                end_time=form.end_time.data,
                order_index=form.order_index.data or 0,
                notes=form.notes.data
            )
            
            db.session.add(course_subject)
            db.session.commit()
            
            # تحديث إحصائيات المادة
            subject = Subject.query.get(form.subject_id.data)
            if subject.usage_stats:
                subject.usage_stats.total_courses += 1
                subject.usage_stats.last_used_date = datetime.now(timezone.utc)
                
                # حساب إجمالي المشاركين في الدورات التي تحتوي على هذه المادة
                total_participants = db.session.query(func.count(CourseParticipant.id)).join(
                    CourseSubject, CourseParticipant.course_id == CourseSubject.course_id
                ).filter(CourseSubject.subject_id == subject.id).scalar() or 0
                
                subject.usage_stats.total_participants = total_participants
                db.session.commit()
            
            flash('تم إضافة المادة للدورة بنجاح!', 'success')
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة المادة: {str(e)}', 'danger')
    
    return redirect(url_for('course_subjects_management', course_id=course_id))

# تحديث مادة في الدورة
@app.route('/course/<int:course_id>/subjects/<int:course_subject_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_course_subject(course_id, course_subject_id):
    """تحديث بيانات المادة في الدورة"""
    
    course = Course.query.get_or_404(course_id)
    course_subject = CourseSubject.query.get_or_404(course_subject_id)
    
    # التحقق من الصلاحيات
    if current_user.role != 'admin' and current_user.id != course.trainer_id:
        flash('ليس لديك صلاحية لتعديل مواد هذه الدورة', 'danger')
        return redirect(url_for('course_details', course_id=course_id))
    
    form = CourseSubjectForm()
    
    # تحديث خيارات القوائم المنسدلة
    available_subjects = Subject.query.filter_by(is_active=True).order_by(Subject.name).all()
    form.subject_id.choices = [(s.id, f"{s.name} ({s.category} - {s.level})") for s in available_subjects]
    form.day_number.choices = [(i, f'اليوم {i}') for i in range(1, course.duration_days + 1)]
    
    if form.validate_on_submit():
        try:
            # التحقق من عدم تكرار المادة في نفس اليوم والوقت (باستثناء السجل الحالي)
            existing = CourseSubject.query.filter(
                and_(
                    CourseSubject.course_id == course_id,
                    CourseSubject.subject_id == form.subject_id.data,
                    CourseSubject.day_number == form.day_number.data,
                    CourseSubject.start_time == form.start_time.data,
                    CourseSubject.id != course_subject_id
                )
            ).first()
            
            if existing:
                flash('هذه المادة موجودة مسبقاً في نفس اليوم والوقت', 'danger')
                return render_template('course_subjects/edit.html', 
                                     title='تحديث المادة', 
                                     form=form, 
                                     course=course, 
                                     course_subject=course_subject)
            
            # التحقق من صحة التوقيت
            try:
                start_time = datetime.strptime(form.start_time.data, '%H:%M').time()
                end_time = datetime.strptime(form.end_time.data, '%H:%M').time()
                
                if start_time >= end_time:
                    flash('وقت البدء يجب أن يكون قبل وقت الانتهاء', 'danger')
                    return render_template('course_subjects/edit.html', 
                                         title='تحديث المادة', 
                                         form=form, 
                                         course=course, 
                                         course_subject=course_subject)
                    
            except ValueError:
                flash('تنسيق الوقت غير صحيح. استخدم تنسيق HH:MM', 'danger')
                return render_template('course_subjects/edit.html', 
                                     title='تحديث المادة', 
                                     form=form, 
                                     course=course, 
                                     course_subject=course_subject)
            
            # تحديث بيانات المادة
            course_subject.subject_id = form.subject_id.data
            course_subject.day_number = form.day_number.data
            course_subject.start_time = form.start_time.data
            course_subject.end_time = form.end_time.data
            course_subject.order_index = form.order_index.data or 0
            course_subject.notes = form.notes.data
            
            db.session.commit()
            flash('تم تحديث المادة بنجاح!', 'success')
            return redirect(url_for('course_subjects_management', course_id=course_id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث المادة: {str(e)}', 'danger')
    
    # ملء النموذج بالبيانات الحالية
    if request.method == 'GET':
        form.subject_id.data = course_subject.subject_id
        form.day_number.data = course_subject.day_number
        form.start_time.data = course_subject.start_time
        form.end_time.data = course_subject.end_time
        form.order_index.data = course_subject.order_index
        form.notes.data = course_subject.notes
    
    return render_template('course_subjects/edit.html',
                         title='تحديث المادة',
                         form=form,
                         course=course,
                         course_subject=course_subject)

# حذف مادة من الدورة
@app.route('/course/<int:course_id>/subjects/<int:course_subject_id>/delete')
@login_required
def remove_subject_from_course(course_id, course_subject_id):
    """حذف مادة من الدورة"""
    
    course = Course.query.get_or_404(course_id)
    course_subject = CourseSubject.query.get_or_404(course_subject_id)
    
    # التحقق من الصلاحيات
    if current_user.role != 'admin' and current_user.id != course.trainer_id:
        flash('ليس لديك صلاحية لحذف مواد من هذه الدورة', 'danger')
        return redirect(url_for('course_details', course_id=course_id))
    
    try:
        subject_name = course_subject.subject.name
        
        # حذف المادة من الدورة
        db.session.delete(course_subject)
        db.session.commit()
        
        # تحديث إحصائيات المادة
        subject = Subject.query.get(course_subject.subject_id)
        if subject and subject.usage_stats:
            # إعادة حساب عدد الدورات
            total_courses = CourseSubject.query.filter_by(subject_id=subject.id).count()
            subject.usage_stats.total_courses = total_courses
            
            # إعادة حساب إجمالي المشاركين
            total_participants = db.session.query(func.count(CourseParticipant.id)).join(
                CourseSubject, CourseParticipant.course_id == CourseSubject.course_id
            ).filter(CourseSubject.subject_id == subject.id).scalar() or 0
            
            subject.usage_stats.total_participants = total_participants
            db.session.commit()
        
        flash(f'تم حذف المادة "{subject_name}" من الدورة بنجاح!', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المادة: {str(e)}', 'danger')
    
    return redirect(url_for('course_subjects_management', course_id=course_id))
