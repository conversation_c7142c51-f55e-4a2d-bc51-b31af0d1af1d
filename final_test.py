#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي شامل
"""

print("🧪 اختبار نهائي شامل...")

try:
    from app import app
    
    with app.app_context():
        # فحص المسارات
        routes = [rule.endpoint for rule in app.url_map.iter_rules()]
        subjects_index_exists = 'subjects_index' in routes
        
        print(f"📊 إجمالي المسارات: {len(routes)}")
        print(f"🎯 subjects_index موجود: {subjects_index_exists}")
        
        # عرض مسارات المواد
        subject_routes = [r for r in routes if 'subject' in r]
        print(f"📚 مسارات المواد ({len(subject_routes)}):")
        for route in subject_routes:
            print(f"   - {route}")
        
        # اختبار dashboard مع تسجيل دخول
        from app import User
        user = User.query.first()
        if user:
            with app.test_client() as client:
                # تسجيل دخول
                with client.session_transaction() as sess:
                    sess['_user_id'] = str(user.id)
                    sess['_fresh'] = True
                
                # اختبار dashboard
                response = client.get('/dashboard')
                print(f"🏠 Dashboard Status: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ Dashboard يعمل بشكل مثالي!")
                    
                    # فحص المحتوى
                    content = response.get_data(as_text=True)
                    if 'إدارة المواد الدراسية' in content:
                        print("✅ رابط إدارة المواد موجود في Dashboard")
                    else:
                        print("⚠️ رابط إدارة المواد غير موجود في Dashboard")
                    
                    if 'BuildError' in content:
                        print("❌ لا تزال هناك BuildError")
                    else:
                        print("✅ لا توجد BuildError - تم الحل نهائياً!")
                
                # اختبار صفحة المواد
                if subjects_index_exists:
                    response2 = client.get('/subjects')
                    print(f"📚 Subjects Status: {response2.status_code}")
                    
                    if response2.status_code == 200:
                        print("✅ صفحة المواد تعمل بشكل مثالي!")
                    else:
                        print("❌ مشكلة في صفحة المواد")
                else:
                    print("❌ مسار subjects_index غير موجود")
        else:
            print("❌ لا يوجد مستخدمين")

except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()

print("\n🏁 انتهى الاختبار النهائي")
