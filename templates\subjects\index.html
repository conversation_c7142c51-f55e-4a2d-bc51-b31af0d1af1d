{% extends "base.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<style>
    .subject-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .subject-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .category-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .level-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        transform: scale(1.05);
    }
    
    .search-section {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .subject-file-icon {
        font-size: 2rem;
        color: #6c757d;
    }
    
    .rating-stars {
        color: #ffc107;
    }
    
    .subject-actions {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .subject-card:hover .subject-actions {
        opacity: 1;
    }
    
    .filter-section {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-book-open text-primary me-2"></i>
                        إدارة المواد الدراسية
                    </h1>
                    <p class="text-muted mt-1">إدارة شاملة للمواد التعليمية والمحتوى الدراسي</p>
                </div>
                <div>
                    <a href="{{ url_for('add_subject') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> إضافة مادة جديدة
                    </a>
                    {% if current_user.role == 'admin' %}
                    <div class="btn-group ms-2">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i> إعدادات
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('subject_categories') }}">
                                <i class="fas fa-tags me-2"></i> إدارة الفئات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('subject_levels') }}">
                                <i class="fas fa-layer-group me-2"></i> إدارة المستويات
                            </a></li>
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">إجمالي المواد</div>
                            <div class="h5 mb-0 font-weight-bold">{{ stats.total_subjects }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-book fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">الفئات</div>
                            <div class="h5 mb-0 font-weight-bold">{{ stats.total_categories }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">الدورات المرتبطة</div>
                            <div class="h5 mb-0 font-weight-bold">{{ stats.total_courses }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-graduation-cap fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">متوسط التقييم</div>
                            <div class="h5 mb-0 font-weight-bold">
                                {{ stats.avg_rating }}
                                <span class="rating-stars">
                                    {% for i in range(1, 6) %}
                                        {% if i <= stats.avg_rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="search-section">
        <form method="GET" action="{{ url_for('subjects_index') }}">
            <div class="row">
                <div class="col-md-4 mb-3">
                    {{ search_form.search_query(class="form-control form-control-lg", placeholder="ابحث في المواد...") }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ search_form.category(class="form-select form-select-lg") }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ search_form.level(class="form-select form-select-lg") }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ search_form.sort_by(class="form-select form-select-lg") }}
                </div>
                <div class="col-md-2 mb-3">
                    <button type="submit" class="btn btn-light btn-lg w-100">
                        <i class="fas fa-search me-1"></i> بحث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Subjects Grid -->
    <div class="row">
        {% for subject in subjects.items %}
        <div class="col-xl-4 col-lg-6 col-md-6 mb-4">
            <div class="card subject-card h-100">
                <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                    <div>
                        <span class="category-badge badge" style="background-color: {{ categories|selectattr('name', 'equalto', subject.category)|first|attr('color') if categories|selectattr('name', 'equalto', subject.category)|first else '#007bff' }}">
                            {{ subject.category }}
                        </span>
                        <span class="level-badge badge bg-secondary ms-1">{{ subject.level }}</span>
                    </div>
                    <div class="subject-actions">
                        <div class="btn-group btn-group-sm">
                            <a href="{{ url_for('subject_details', subject_id=subject.id) }}" class="btn btn-outline-primary btn-sm" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if current_user.role == 'admin' or current_user.id == subject.created_by %}
                            <a href="{{ url_for('edit_subject', subject_id=subject.id) }}" class="btn btn-outline-warning btn-sm" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ url_for('delete_subject', subject_id=subject.id) }}" class="btn btn-outline-danger btn-sm" title="حذف" 
                               onclick="return confirm('هل أنت متأكد من حذف هذه المادة؟')">
                                <i class="fas fa-trash"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="text-center mb-3">
                        {% if subject.file_path %}
                            <i class="subject-file-icon fas fa-file-{{ 'pdf' if subject.file_type == 'pdf' else 'alt' }}"></i>
                        {% else %}
                            <i class="subject-file-icon fas fa-book"></i>
                        {% endif %}
                    </div>
                    
                    <h5 class="card-title text-center mb-3">
                        <a href="{{ url_for('subject_details', subject_id=subject.id) }}" class="text-decoration-none">
                            {{ subject.name }}
                        </a>
                    </h5>
                    
                    {% if subject.description %}
                    <p class="card-text text-muted small">
                        {{ subject.description[:100] }}{% if subject.description|length > 100 %}...{% endif %}
                    </p>
                    {% endif %}
                    
                    <div class="row text-center small text-muted">
                        <div class="col-4">
                            <i class="fas fa-clock me-1"></i>
                            {{ subject.duration_hours }}ساعة
                        </div>
                        <div class="col-4">
                            <i class="fas fa-graduation-cap me-1"></i>
                            {{ subject.usage_stats.total_courses if subject.usage_stats else 0 }}
                        </div>
                        <div class="col-4">
                            <i class="fas fa-star me-1 text-warning"></i>
                            {{ "%.1f"|format(subject.usage_stats.average_rating if subject.usage_stats else 0) }}
                        </div>
                    </div>
                </div>
                
                <div class="card-footer bg-transparent border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            {{ subject.creator.username if subject.creator else 'غير محدد' }}
                        </small>
                        <small class="text-muted">
                            {{ subject.created_at.strftime('%Y-%m-%d') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if subjects.pages > 1 %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="تصفح المواد">
                <ul class="pagination justify-content-center">
                    {% if subjects.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('subjects_index', page=subjects.prev_num, **request.args) }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in subjects.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != subjects.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('subjects_index', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if subjects.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('subjects_index', page=subjects.next_num, **request.args) }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    <!-- Empty State -->
    {% if not subjects.items %}
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-book-open fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مواد دراسية</h4>
                <p class="text-muted">ابدأ بإضافة مواد دراسية جديدة لبناء مكتبة المحتوى التعليمي</p>
                <a href="{{ url_for('add_subject') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> إضافة أول مادة
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تأثيرات بصرية للبطاقات
    $('.subject-card').hover(
        function() {
            $(this).find('.subject-actions').addClass('show');
        },
        function() {
            $(this).find('.subject-actions').removeClass('show');
        }
    );
    
    // تحديث الرابط عند تغيير خيارات البحث
    $('.form-select').change(function() {
        $(this).closest('form').submit();
    });
});
</script>
{% endblock %}
