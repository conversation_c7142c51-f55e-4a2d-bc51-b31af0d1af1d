#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام إدارة المواد الدراسية
"""

print("🚀 بدء اختبار نظام إدارة المواد...")

try:
    # استيراد التطبيق
    from app import app, db
    print("✅ تم استيراد التطبيق بنجاح")
    
    # استيراد النماذج
    from app import Subject, SubjectCategory, SubjectLevel, CourseSubject
    print("✅ تم استيراد النماذج بنجاح")
    
    # اختبار الاتصال بقاعدة البيانات
    with app.app_context():
        # إنشاء الجداول إذا لم تكن موجودة
        db.create_all()
        print("✅ تم إنشاء الجداول بنجاح")
        
        # اختبار إضافة فئة
        test_category = SubjectCategory.query.filter_by(name='اختبار').first()
        if not test_category:
            test_category = SubjectCategory(
                name='اختبار',
                description='فئة اختبار',
                color='#007bff',
                icon='fas fa-test'
            )
            db.session.add(test_category)
            db.session.commit()
            print("✅ تم إضافة فئة اختبار")
        
        # اختبار إضافة مستوى
        test_level = SubjectLevel.query.filter_by(name='اختبار').first()
        if not test_level:
            test_level = SubjectLevel(
                name='اختبار',
                description='مستوى اختبار',
                order_index=1,
                color='#28a745'
            )
            db.session.add(test_level)
            db.session.commit()
            print("✅ تم إضافة مستوى اختبار")
        
        # عرض الإحصائيات
        categories_count = SubjectCategory.query.count()
        levels_count = SubjectLevel.query.count()
        subjects_count = Subject.query.count()
        
        print(f"📊 الإحصائيات:")
        print(f"   - الفئات: {categories_count}")
        print(f"   - المستويات: {levels_count}")
        print(f"   - المواد: {subjects_count}")
        
    print("🎉 تم اختبار النظام بنجاح!")
    
except Exception as e:
    print(f"❌ خطأ في الاختبار: {str(e)}")
    import traceback
    traceback.print_exc()
