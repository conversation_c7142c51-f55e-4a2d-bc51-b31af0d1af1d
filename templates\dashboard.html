{% extends "layout.html" %}

{% block styles %}
<style>
    /* خلفية رسمية متحركة للصفحة */
    body {
        background:
            linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #60a5fa 50%, #93c5fd 75%, #dbeafe 100%);
        background-size: 400% 400%;
        animation: gradientShift 20s ease infinite;
        min-height: 100vh;
        position: relative;
        overflow-x: hidden;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(30, 58, 138, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(147, 197, 253, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 60% 70%, rgba(219, 234, 254, 0.1) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
        animation: floatingBubbles 25s ease-in-out infinite;
    }

    @keyframes floatingBubbles {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-15px) rotate(120deg); }
        66% { transform: translateY(8px) rotate(240deg); }
    }

    /* تصميم حديث للبطاقات مع تأثيرات زجاجية */
    .dashboard-card {
        border-radius: 25px;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        backdrop-filter: blur(20px);
        background:
            linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        position: relative;
    }

    .dashboard-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .dashboard-card:hover::before {
        opacity: 1;
    }

    .dashboard-card:hover {
        transform: translateY(-15px) scale(1.03);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.25),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .dashboard-card-header {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 25px 30px;
        font-weight: 700;
        font-size: 1.2rem;
        position: relative;
        overflow: hidden;
        border-radius: 25px 25px 0 0;
    }

    .dashboard-card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .dashboard-card:hover .dashboard-card-header::before {
        left: 100%;
    }

    /* بطاقات الإحصائيات المحسنة مع تأثيرات ثلاثية الأبعاد */
    .stat-card {
        border-radius: 25px;
        padding: 35px 25px;
        text-align: center;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 2px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(15px);
        transform-style: preserve-3d;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
        opacity: 0;
        transition: opacity 0.3s;
    }

    .stat-card:hover::before {
        opacity: 1;
    }

    .stat-card:hover {
        transform: translateY(-15px) scale(1.08) rotateX(5deg);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.4);
        border-color: rgba(255, 255, 255, 0.4);
        filter: brightness(1.1);
    }

    .stat-primary {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        box-shadow: 0 15px 40px rgba(30, 64, 175, 0.4);
        border: 2px solid rgba(59, 130, 246, 0.3);
    }

    .stat-success {
        background: linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%);
        color: white;
        box-shadow: 0 15px 40px rgba(34, 211, 238, 0.4);
        border: 2px solid rgba(6, 182, 212, 0.3);
    }

    .stat-warning {
        background: linear-gradient(135deg, #374151 0%, #6b7280 100%);
        color: white;
        box-shadow: 0 15px 40px rgba(55, 65, 81, 0.4);
        border: 2px solid rgba(107, 114, 128, 0.3);
    }

    .stat-danger {
        background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
        color: white;
        box-shadow: 0 15px 40px rgba(30, 58, 138, 0.4);
        border: 2px solid rgba(55, 48, 163, 0.3);
    }

    .stat-info {
        background: linear-gradient(135deg, #0f172a 0%, #334155 100%);
        color: white;
        box-shadow: 0 15px 40px rgba(15, 23, 42, 0.4);
        border: 2px solid rgba(51, 65, 85, 0.3);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 800;
        margin: 15px 0;
        color: white !important;
        text-shadow: 0 3px 6px rgba(0,0,0,0.5);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        color: white !important;
        opacity: 0.95;
        text-shadow: 0 2px 4px rgba(0,0,0,0.4);
    }

    .stat-label {
        font-size: 0.9rem;
        color: white !important;
        opacity: 0.95;
        margin-bottom: 10px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.4);
    }

    /* تحسين الشريط الجانبي بتصميم زجاجي */
    .sidebar {
        background:
            linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(30, 64, 175, 0.95) 50%, rgba(59, 130, 246, 0.95) 100%);
        color: white;
        min-height: calc(100vh - 56px);
        padding-top: 35px;
        border-radius: 25px;
        box-shadow:
            0 25px 50px rgba(30, 58, 138, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.15);
        position: relative;
        overflow: hidden;
    }

    .sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    /* تصميم صورة المستخدم الافتراضية */
    .user-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        box-shadow:
            0 15px 30px rgba(30, 64, 175, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 3px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .user-avatar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
        border-radius: 50%;
    }

    .user-avatar i {
        font-size: 2.5rem;
        color: white;
        z-index: 2;
        position: relative;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .user-avatar:hover {
        transform: scale(1.05);
        box-shadow:
            0 20px 40px rgba(30, 64, 175, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .sidebar-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 15px 20px;
        display: block;
        text-decoration: none;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border-radius: 15px;
        margin: 8px 15px;
        position: relative;
        overflow: hidden;
        font-weight: 500;
    }

    .sidebar-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .sidebar-link:hover::before {
        left: 100%;
    }

    .sidebar-link:hover, .sidebar-link.active {
        color: white;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.4) 0%, rgba(147, 197, 253, 0.3) 100%);
        transform: translateX(8px);
        box-shadow:
            0 8px 20px rgba(59, 130, 246, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .sidebar-link i {
        margin-left: 10px;
        width: 20px;
        text-align: center;
    }

    .sidebar-dropdown-menu {
        display: none;
        padding-right: 20px;
    }

    .sidebar-dropdown-menu.show {
        display: block;
    }

    .dropdown-toggle::after {
        display: inline-block;
        margin-right: 5px;
        vertical-align: middle;
        content: "";
        border-top: 0.3em solid;
        border-left: 0.3em solid transparent;
        border-right: 0.3em solid transparent;
    }

    /* أنماط المربعات الملونة الكبيرة */
    .category-card {
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        height: 100%;
        position: relative;
        overflow: hidden;
        color: white;
        z-index: 1;
    }

    .category-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(-100%);
        transition: all 0.5s;
        z-index: -1;
    }

    .category-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    }

    .category-card:hover::before {
        transform: translateX(0);
    }

    .category-icon {
        font-size: 3rem;
        margin-bottom: 20px;
        color: white !important;
        opacity: 0.9;
        text-shadow: 0 2px 4px rgba(0,0,0,0.4);
    }

    .category-title {
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 15px;
        color: white !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    }

    .category-description {
        font-size: 1rem;
        color: white !important;
        opacity: 0.9;
        text-shadow: 0 1px 3px rgba(0,0,0,0.4);
    }

    .category-primary {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        box-shadow:
            0 20px 40px rgba(30, 64, 175, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(59, 130, 246, 0.3);
    }

    .category-success {
        background: linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%);
        box-shadow:
            0 20px 40px rgba(34, 211, 238, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(6, 182, 212, 0.3);
    }

    .category-info {
        background: linear-gradient(135deg, #374151 0%, #6b7280 100%);
        box-shadow:
            0 20px 40px rgba(55, 65, 81, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(107, 114, 128, 0.3);
    }

    .category-warning {
        background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
        box-shadow:
            0 20px 40px rgba(30, 58, 138, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(55, 48, 163, 0.3);
    }

    .category-danger {
        background: linear-gradient(135deg, #0f172a 0%, #334155 100%);
        box-shadow:
            0 20px 40px rgba(15, 23, 42, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(51, 65, 85, 0.3);
    }

    /* تحسين العنوان الرئيسي بتصميم متطور */
    .page-title {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 900;
        font-size: 3rem;
        margin-bottom: 40px;
        text-align: center;
        position: relative;
        filter: drop-shadow(0 4px 8px rgba(30, 64, 175, 0.4));
        text-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
    }

    .page-title i {
        margin-left: 15px;
        animation: rotate 4s linear infinite;
        filter: drop-shadow(0 0 10px rgba(30, 64, 175, 0.5));
    }

    .page-title::after {
        content: '';
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 150px;
        height: 6px;
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #93c5fd 100%);
        border-radius: 3px;
        box-shadow:
            0 4px 12px rgba(30, 64, 175, 0.5),
            0 0 20px rgba(59, 130, 246, 0.3);
        animation: glow 3s ease-in-out infinite;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes glow {
        0%, 100% { box-shadow: 0 4px 12px rgba(30, 64, 175, 0.5), 0 0 20px rgba(59, 130, 246, 0.3); }
        50% { box-shadow: 0 4px 12px rgba(30, 64, 175, 0.8), 0 0 30px rgba(59, 130, 246, 0.6); }
    }

    /* تأثيرات الحركة */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .dashboard-card, .stat-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .stat-card:nth-child(2) { animation-delay: 0.1s; }
    .stat-card:nth-child(3) { animation-delay: 0.2s; }
    .stat-card:nth-child(4) { animation-delay: 0.3s; }
    .stat-card:nth-child(5) { animation-delay: 0.4s; }

    .stat-icon {
        animation: floatIcon 3s ease-in-out infinite;
        position: relative;
    }

    .stat-icon::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 120%;
        height: 120%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
        border-radius: 50%;
        opacity: 0;
        animation: iconGlow 3s ease-in-out infinite;
    }

    @keyframes floatIcon {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-8px) rotate(5deg); }
    }

    @keyframes iconGlow {
        0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
        50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
    }

    /* تحسين الحاوي الرئيسي بتصميم زجاجي متطور */
    .main-container {
        background:
            linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(25px);
        border-radius: 30px;
        padding: 40px;
        margin-top: 25px;
        box-shadow:
            0 30px 60px rgba(30, 64, 175, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
    }

    .main-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #1e40af, #3b82f6, #60a5fa, #93c5fd);
        background-size: 300% 100%;
        animation: shimmer 4s ease-in-out infinite;
    }

    .main-container::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
        opacity: 0.5;
    }

    @keyframes shimmer {
        0% { background-position: -300% 0; }
        100% { background-position: 300% 0; }
    }

    /* تحسين الأزرار السريعة */
    .quick-actions {
        margin-top: 40px;
    }

    .action-btn {
        border-radius: 15px;
        padding: 18px 25px;
        margin: 8px;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: none;
        font-weight: 600;
        font-size: 1rem;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    .action-btn:hover {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link active">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="http://localhost:5000/person_data/name_analysis" class="sidebar-link">
                <i class="fas fa-chart-pie"></i> المباينة
            </a>
            <a href="{{ url_for('person_data.person_data_table') }}" class="sidebar-link">
                <i class="fas fa-table"></i> جدول بيانات الأشخاص
            </a>
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link">
                <i class="fas fa-table"></i> الجداول الترميزية
            </a>
            <a href="{{ url_for('subjects_index') }}" class="sidebar-link">
                <i class="fas fa-book-open"></i> إدارة المواد الدراسية
            </a>
            <a href="{{ url_for('backup') }}" class="sidebar-link">
                <i class="fas fa-database"></i> النسخ الاحتياطي
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            {% if current_user.role == 'admin' %}
            <a href="/reports/dashboard" class="sidebar-link">
                <i class="fas fa-chart-line"></i> التقارير التفاعلية
            </a>
            {% endif %}
            {% if current_user.role == 'admin' %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
            {% endif %}
        </div>
    </div>

    <div class="col-md-9">
        <div class="main-container">
            <h2 class="page-title"><i class="fas fa-cogs"></i> لوحة التحكم</h2>



            <div class="row mb-5">
                <div class="col-md-3">
                    <div class="stat-card stat-primary">
                        <i class="fas fa-users stat-icon"></i>
                        <div class="stat-number">1,016</div>
                        <div class="stat-label">إجمالي الأشخاص</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-success">
                        <i class="fas fa-graduation-cap stat-icon"></i>
                        <div class="stat-number">201</div>
                        <div class="stat-label">الدورات المتاحة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-info">
                        <i class="fas fa-user-graduate stat-icon"></i>
                        <div class="stat-number">5,720</div>
                        <div class="stat-label">إجمالي المشاركين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-warning">
                        <i class="fas fa-chart-line stat-icon"></i>
                        <div class="stat-number">95%</div>
                        <div class="stat-label">معدل النجاح</div>
                    </div>
                </div>
            </div>

        <!-- المربعات الملونة الكبيرة -->
        <div class="row mb-5">
            <h3 class="mb-4">إدارة النظام</h3>

            <div class="col-md-6 mb-4">
                <a href="{{ url_for('personal_data_list') }}" class="text-decoration-none">
                    <div class="category-card category-primary">
                        <div class="category-icon">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <h3 class="category-title">الملتحقين</h3>
                        <p class="category-description">إدارة بيانات المتدربين والملتحقين بالدورات</p>
                    </div>
                </a>
            </div>

            <div class="col-md-6 mb-4">
                <a href="{{ url_for('graduates') }}" class="text-decoration-none">
                    <div class="category-card category-success">
                        <div class="category-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3 class="category-title">الخريجين</h3>
                        <p class="category-description">عرض بيانات الخريجين من الدورات</p>
                    </div>
                </a>
            </div>

            {% if current_user.role == 'admin' %}
            <div class="col-md-6 mb-4">
                <a href="/reports/dashboard" class="text-decoration-none">
                    <div class="category-card category-info">
                        <div class="category-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="category-title">التقارير التفاعلية</h3>
                        <p class="category-description">تقارير بصرية تفاعلية مع رسوم بيانية متطورة</p>
                    </div>
                </a>
            </div>

            <div class="col-md-6 mb-4">
                <a href="{{ url_for('backup') }}" class="text-decoration-none">
                    <div class="category-card category-warning">
                        <div class="category-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3 class="category-title">النسخ الاحتياطي</h3>
                        <p class="category-description">إنشاء وإدارة النسخ الاحتياطية مع عرض تاريخ الإنشاء</p>
                    </div>
                </a>
            </div>
            {% endif %}
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="dashboard-card mb-4">
                    <div class="dashboard-card-header">
                        <i class="fas fa-calendar-alt me-2"></i> الدورات القادمة
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>تطوير تطبيقات الويب</span>
                                <span class="badge bg-primary rounded-pill">غداً</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>أساسيات قواعد البيانات</span>
                                <span class="badge bg-primary rounded-pill">بعد 3 أيام</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>إدارة المشاريع الاحترافية</span>
                                <span class="badge bg-primary rounded-pill">بعد أسبوع</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="dashboard-card mb-4">
                    <div class="dashboard-card-header">
                        <i class="fas fa-bell me-2"></i> آخر الإشعارات
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success mb-2">
                            <small class="text-muted">منذ ساعتين</small>
                            <p class="mb-0">تم إضافة دورة جديدة: "تطوير تطبيقات الهاتف المحمول"</p>
                        </div>
                        <div class="alert alert-info mb-2">
                            <small class="text-muted">منذ 5 ساعات</small>
                            <p class="mb-0">تم تحديث جدول الدورات التدريبية للشهر القادم</p>
                        </div>
                        <div class="alert alert-warning mb-2">
                            <small class="text-muted">منذ يوم</small>
                            <p class="mb-0">تذكير: موعد تسليم المشروع النهائي بعد أسبوع</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div> <!-- إغلاق main-container -->
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تفعيل القائمة المنسدلة للجداول الترميزية
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdownMenu = this.nextElementSibling;
                dropdownMenu.classList.toggle('show');
            });
        });
    });
</script>
{% endblock %}