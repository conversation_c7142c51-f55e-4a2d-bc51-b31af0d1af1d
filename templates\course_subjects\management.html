{% extends "dashboard.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<style>
    .course-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .day-section {
        background: white;
        border-radius: 10px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        overflow: hidden;
    }
    
    .day-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 1rem 1.5rem;
        font-weight: 600;
    }
    
    .subject-item {
        border-left: 4px solid #007bff;
        background: #f8f9fa;
        padding: 1rem;
        margin: 0.5rem 1rem;
        border-radius: 5px;
        transition: all 0.3s ease;
    }
    
    .subject-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }
    
    .time-badge {
        background: #007bff;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
    }
    
    .category-badge {
        background: #28a745;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
    }
    
    .level-badge {
        background: #ffc107;
        color: #212529;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
    }
    
    .add-subject-form {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    }
    
    .stats-value {
        font-size: 2rem;
        font-weight: bold;
        color: #007bff;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .empty-day {
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }
    
    .subject-actions {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .subject-item:hover .subject-actions {
        opacity: 1;
    }
    
    .timeline {
        position: relative;
        padding-left: 2rem;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 1rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 1rem;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -1.5rem;
        top: 0.5rem;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    
    <!-- Course Header -->
    <div class="course-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 mb-2">
                    <i class="fas fa-graduation-cap me-2"></i>
                    إدارة مواد الدورة: {{ course.title }}
                </h1>
                <p class="mb-0 opacity-75">رقم الدورة: {{ course.course_number }} | المدة: {{ course.duration_days }} أيام</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ url_for('course_details', course_id=course.id) }}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-1"></i> العودة للدورة
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-value">{{ stats.total_subjects }}</div>
                <div class="stats-label">إجمالي المواد</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-value">{{ stats.total_hours }}</div>
                <div class="stats-label">إجمالي الساعات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-value">{{ stats.available_count }}</div>
                <div class="stats-label">مواد متاحة للإضافة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-value">{{ course.duration_days }}</div>
                <div class="stats-label">أيام الدورة</div>
            </div>
        </div>
    </div>

    <!-- Add Subject Form -->
    <div class="add-subject-form">
        <h5 class="mb-3">
            <i class="fas fa-plus-circle me-2"></i>إضافة مادة جديدة للدورة
        </h5>
        
        <form method="POST" action="{{ url_for('add_subject_to_course', course_id=course.id) }}">
            {{ form.hidden_tag() }}
            
            <div class="row">
                <div class="col-md-3 mb-3">
                    {{ form.subject_id.label(class="form-label") }}
                    {{ form.subject_id(class="form-select form-select-lg") }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ form.day_number.label(class="form-label") }}
                    {{ form.day_number(class="form-select form-select-lg") }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ form.start_time.label(class="form-label") }}
                    {{ form.start_time(class="form-control form-control-lg", type="time") }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ form.end_time.label(class="form-label") }}
                    {{ form.end_time(class="form-control form-control-lg", type="time") }}
                </div>
                <div class="col-md-3 mb-3">
                    {{ form.notes.label(class="form-label") }}
                    {{ form.notes(class="form-control form-control-lg", placeholder="ملاحظات اختيارية") }}
                </div>
            </div>
            
            <div class="text-center">
                {{ form.submit(class="btn btn-light btn-lg px-4") }}
            </div>
        </form>
    </div>

    <!-- Course Schedule by Days -->
    {% for day in range(1, course.duration_days + 1) %}
    <div class="day-section">
        <div class="day-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>اليوم {{ day }}
                    </h5>
                </div>
                <div class="col-md-6 text-end">
                    <small>عدد المواد: {{ subjects_by_day[day]|length }}</small>
                </div>
            </div>
        </div>
        
        <div class="day-content">
            {% if subjects_by_day[day] %}
                <div class="timeline">
                    {% for course_subject, subject in subjects_by_day[day] %}
                    <div class="timeline-item">
                        <div class="subject-item">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h6 class="mb-1">
                                        <a href="{{ url_for('subject_details', subject_id=subject.id) }}" class="text-decoration-none">
                                            {{ subject.name }}
                                        </a>
                                    </h6>
                                    <div class="mb-2">
                                        <span class="category-badge me-1">{{ subject.category }}</span>
                                        <span class="level-badge me-1">{{ subject.level }}</span>
                                        <span class="time-badge">{{ course_subject.start_time }} - {{ course_subject.end_time }}</span>
                                    </div>
                                    {% if subject.description %}
                                    <p class="text-muted small mb-0">
                                        {{ subject.description[:100] }}{% if subject.description|length > 100 %}...{% endif %}
                                    </p>
                                    {% endif %}
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>{{ subject.duration_hours }} ساعة<br>
                                        <i class="fas fa-sort-numeric-up me-1"></i>ترتيب: {{ course_subject.order_index }}
                                    </small>
                                </div>
                                <div class="col-md-3 text-end">
                                    <div class="subject-actions">
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_course_subject', course_id=course.id, course_subject_id=course_subject.id) }}" 
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('remove_subject_from_course', course_id=course.id, course_subject_id=course_subject.id) }}" 
                                               class="btn btn-outline-danger" title="حذف"
                                               onclick="return confirm('هل أنت متأكد من حذف هذه المادة من الدورة؟')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% if course_subject.notes %}
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-sticky-note me-1"></i>{{ course_subject.notes }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-day">
                    <i class="fas fa-calendar-plus fa-3x mb-3"></i>
                    <h6>لا توجد مواد مجدولة لهذا اليوم</h6>
                    <p class="text-muted">استخدم النموذج أعلاه لإضافة مواد لهذا اليوم</p>
                </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}

    <!-- Available Subjects for Quick Add -->
    {% if available_for_add %}
    <div class="card">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-plus-square me-2"></i>مواد متاحة للإضافة السريعة
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for subject in available_for_add[:12] %}
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body">
                            <h6 class="card-title">{{ subject.name }}</h6>
                            <div class="mb-2">
                                <span class="badge bg-secondary">{{ subject.category }}</span>
                                <span class="badge bg-info">{{ subject.level }}</span>
                            </div>
                            <p class="card-text small text-muted">
                                {{ subject.description[:80] if subject.description else 'لا يوجد وصف' }}
                                {% if subject.description and subject.description|length > 80 %}...{% endif %}
                            </p>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">{{ subject.duration_hours }} ساعة</small>
                                <a href="{{ url_for('subject_details', subject_id=subject.id) }}" class="btn btn-sm btn-outline-primary">
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if available_for_add|length > 12 %}
            <div class="text-center mt-3">
                <a href="{{ url_for('subjects_index') }}" class="btn btn-outline-info">
                    عرض جميع المواد المتاحة ({{ available_for_add|length }})
                </a>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    
    // تأثيرات بصرية للمواد
    $('.subject-item').hover(
        function() {
            $(this).find('.subject-actions').addClass('show');
        },
        function() {
            $(this).find('.subject-actions').removeClass('show');
        }
    );
    
    // تحديث وقت الانتهاء تلقائياً عند تغيير وقت البدء
    $('#start_time').change(function() {
        const startTime = $(this).val();
        if (startTime) {
            const [hours, minutes] = startTime.split(':');
            const startDate = new Date();
            startDate.setHours(parseInt(hours), parseInt(minutes));
            
            // إضافة ساعة واحدة افتراضياً
            const endDate = new Date(startDate.getTime() + 60 * 60 * 1000);
            const endTime = endDate.toTimeString().slice(0, 5);
            
            $('#end_time').val(endTime);
        }
    });
    
    // التحقق من صحة الأوقات
    $('#end_time').change(function() {
        const startTime = $('#start_time').val();
        const endTime = $(this).val();
        
        if (startTime && endTime && startTime >= endTime) {
            alert('وقت الانتهاء يجب أن يكون بعد وقت البدء');
            $(this).focus();
        }
    });
    
    // تحديث خيارات المواد عند تغيير اليوم
    $('#day_number').change(function() {
        const selectedDay = $(this).val();
        console.log('تم اختيار اليوم:', selectedDay);
        // يمكن إضافة منطق لفلترة المواد حسب اليوم إذا لزم الأمر
    });
    
    // تأكيد الحذف
    $('.btn-outline-danger').click(function(e) {
        if (!confirm('هل أنت متأكد من حذف هذه المادة من الدورة؟\nسيتم حذف الجدولة فقط وليس المادة نفسها.')) {
            e.preventDefault();
        }
    });
    
});
</script>
{% endblock %}
