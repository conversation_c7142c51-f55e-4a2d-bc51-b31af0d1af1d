{% extends "base.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<style>
    .form-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .form-card {
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border-radius: 15px;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .file-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }
    
    .file-upload-area:hover {
        border-color: #667eea;
        background: #f0f2ff;
    }
    
    .file-upload-area.dragover {
        border-color: #667eea;
        background: #e8ecff;
    }
    
    .preview-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 1rem;
    }
    
    .tag-input {
        position: relative;
    }
    
    .tag-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }
    
    .tag-suggestion {
        padding: 0.5rem 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .tag-suggestion:hover {
        background: #f8f9fa;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    
    <!-- Header Section -->
    <div class="form-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h3 mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    إضافة مادة دراسية جديدة
                </h1>
                <p class="mb-0 mt-2 opacity-75">أضف مادة تعليمية جديدة إلى مكتبة المحتوى الدراسي</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ url_for('subjects_index') }}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-1"></i> العودة للمواد
                </a>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <div class="row justify-content-center">
        <div class="col-xl-8 col-lg-10">
            <div class="card form-card">
                <div class="card-body p-4">
                    
                    <form method="POST" enctype="multipart/form-data" id="subjectForm">
                        {{ form.hidden_tag() }}
                        
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                                </h5>
                            </div>
                            
                            <div class="col-md-12 mb-3">
                                {{ form.name.label(class="form-label required-field") }}
                                {{ form.name(class="form-control form-control-lg", placeholder="أدخل اسم المادة الدراسية") }}
                                {% if form.name.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.name.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.category.label(class="form-label required-field") }}
                                {{ form.category(class="form-select form-select-lg") }}
                                {% if form.category.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.category.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.level.label(class="form-label required-field") }}
                                {{ form.level(class="form-select form-select-lg") }}
                                {% if form.level.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.level.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-12 mb-3">
                                {{ form.description.label(class="form-label") }}
                                {{ form.description(class="form-control", rows="4", placeholder="وصف تفصيلي للمادة ومحتواها التعليمي") }}
                                {% if form.description.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.description.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Duration and File -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-clock me-2"></i>التوقيت والملفات
                                </h5>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.duration_hours.label(class="form-label required-field") }}
                                <div class="input-group">
                                    {{ form.duration_hours(class="form-control form-control-lg", min="1", max="24") }}
                                    <span class="input-group-text">ساعة</span>
                                </div>
                                {% if form.duration_hours.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.duration_hours.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.tags.label(class="form-label") }}
                                <div class="tag-input">
                                    {{ form.tags(class="form-control form-control-lg", placeholder="الكلمات المفتاحية مفصولة بفواصل") }}
                                    <div class="tag-suggestions" id="tagSuggestions"></div>
                                </div>
                                <small class="text-muted">مثال: برمجة, تطوير, ويب, جافاسكريبت</small>
                                {% if form.tags.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.tags.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-12 mb-3">
                                {{ form.file.label(class="form-label") }}
                                <div class="file-upload-area" id="fileUploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">اسحب الملف هنا أو انقر للاختيار</h5>
                                    <p class="text-muted mb-0">الملفات المدعومة: PDF, PPT, DOC, XLS, صور, فيديو</p>
                                    {{ form.file(class="form-control", style="display: none;", id="fileInput") }}
                                </div>
                                <div id="filePreview" class="mt-2" style="display: none;"></div>
                                {% if form.file.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.file.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Preview Content -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-eye me-2"></i>محتوى المعاينة
                                </h5>
                            </div>
                            
                            <div class="col-12 mb-3">
                                {{ form.preview_content.label(class="form-label") }}
                                {{ form.preview_content(class="form-control", rows="6", placeholder="محتوى تعريفي بالمادة يظهر للمستخدمين قبل تحميل الملف الكامل") }}
                                <small class="text-muted">هذا المحتوى سيظهر كمعاينة للمادة قبل التحميل</small>
                                {% if form.preview_content.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.preview_content.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-12 text-center">
                                {{ form.submit(class="btn btn-primary btn-lg px-5") }}
                                <a href="{{ url_for('subjects_index') }}" class="btn btn-outline-secondary btn-lg px-5 ms-3">إلغاء</a>
                            </div>
                        </div>
                        
                    </form>
                    
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    
    // File upload handling
    const fileUploadArea = $('#fileUploadArea');
    const fileInput = $('#fileInput');
    const filePreview = $('#filePreview');
    
    // Click to select file
    fileUploadArea.click(function() {
        fileInput.click();
    });
    
    // Drag and drop
    fileUploadArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });
    
    fileUploadArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });
    
    fileUploadArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            fileInput[0].files = files;
            showFilePreview(files[0]);
        }
    });
    
    // File input change
    fileInput.change(function() {
        if (this.files.length > 0) {
            showFilePreview(this.files[0]);
        }
    });
    
    function showFilePreview(file) {
        const fileName = file.name;
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        const fileType = file.type;
        
        let icon = 'fas fa-file';
        if (fileType.includes('pdf')) icon = 'fas fa-file-pdf text-danger';
        else if (fileType.includes('image')) icon = 'fas fa-file-image text-success';
        else if (fileType.includes('video')) icon = 'fas fa-file-video text-info';
        else if (fileType.includes('presentation') || fileName.includes('.ppt')) icon = 'fas fa-file-powerpoint text-warning';
        else if (fileType.includes('document') || fileName.includes('.doc')) icon = 'fas fa-file-word text-primary';
        else if (fileType.includes('spreadsheet') || fileName.includes('.xls')) icon = 'fas fa-file-excel text-success';
        
        filePreview.html(`
            <div class="alert alert-info d-flex align-items-center">
                <i class="${icon} fa-2x me-3"></i>
                <div>
                    <strong>${fileName}</strong><br>
                    <small class="text-muted">حجم الملف: ${fileSize} ميجابايت</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger ms-auto" onclick="clearFile()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).show();
    }
    
    // Clear file
    window.clearFile = function() {
        fileInput.val('');
        filePreview.hide();
    };
    
    // Tag suggestions
    const commonTags = [
        'برمجة', 'تطوير', 'ويب', 'جافاسكريبت', 'بايثون', 'جافا', 'سي شارب',
        'إدارة', 'قيادة', 'تسويق', 'مبيعات', 'محاسبة', 'مالية',
        'تصميم', 'جرافيك', 'فوتوشوب', 'إليستريتور',
        'إنجليزية', 'عربية', 'فرنسية', 'ألمانية',
        'رياضيات', 'فيزياء', 'كيمياء', 'أحياء',
        'هندسة', 'كهرباء', 'ميكانيكا', 'مدني'
    ];
    
    $('#tags').on('input', function() {
        const value = $(this).val().toLowerCase();
        const lastTag = value.split(',').pop().trim();
        
        if (lastTag.length > 1) {
            const suggestions = commonTags.filter(tag => 
                tag.toLowerCase().includes(lastTag) && 
                !value.includes(tag)
            );
            
            if (suggestions.length > 0) {
                const suggestionsHtml = suggestions.map(tag => 
                    `<div class="tag-suggestion" onclick="addTag('${tag}')">${tag}</div>`
                ).join('');
                
                $('#tagSuggestions').html(suggestionsHtml).show();
            } else {
                $('#tagSuggestions').hide();
            }
        } else {
            $('#tagSuggestions').hide();
        }
    });
    
    window.addTag = function(tag) {
        const tagsInput = $('#tags');
        const currentValue = tagsInput.val();
        const tags = currentValue.split(',').map(t => t.trim());
        tags.pop(); // Remove the last incomplete tag
        tags.push(tag);
        tagsInput.val(tags.join(', ') + ', ');
        $('#tagSuggestions').hide();
        tagsInput.focus();
    };
    
    // Hide suggestions when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('.tag-input').length) {
            $('#tagSuggestions').hide();
        }
    });
    
    // Form validation
    $('#subjectForm').submit(function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = ['name', 'category', 'level', 'duration_hours'];
        requiredFields.forEach(field => {
            const input = $(`#${field}`);
            if (!input.val().trim()) {
                input.addClass('is-invalid');
                isValid = false;
            } else {
                input.removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: $('.is-invalid').first().offset().top - 100
            }, 500);
        }
    });
    
    // Remove validation errors on input
    $('.form-control, .form-select').on('input change', function() {
        $(this).removeClass('is-invalid');
    });
    
});
</script>
{% endblock %}
